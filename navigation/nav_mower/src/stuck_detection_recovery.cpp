#include "stuck_detection_recovery.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <iomanip>
#include <sstream>

namespace fescue_iox
{

StuckDetectionRecovery::StuckDetectionRecovery(const StuckRecoveryParam &param)
    : param_(param)
    , current_linear_speed_(param.initial_linear_speed)
    , current_angular_speed_(param.initial_angular_speed)
    , recovery_start_time_(0)
    , last_action_time_(0)
    , recovery_cycle_count_(0)
{
    LOG_INFO("[StuckDetectionRecovery] Initialize stuck recovery system");

    // Initialize sliding windows
    InitializeSlidingWindows();
}

StuckDetectionRecovery::~StuckDetectionRecovery()
{
    Shutdown();
    LOG_INFO("[StuckDetectionRecovery] Stuck recovery system shutdown");
}

void StuckDetectionRecovery::Initialize()
{
    LOG_INFO("[StuckDetectionRecovery] Initialize stuck detection and recovery system");

    // Initialize data logging
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    // Start detection thread (but not activated)
    detection_running_.store(true);
    detection_thread_ = std::thread(&StuckDetectionRecovery::DetectionThread, this);

    // Start recovery thread
    recovery_running_.store(true);
    recovery_thread_ = std::thread(&StuckDetectionRecovery::RecoveryThread, this);

    LOG_INFO("[StuckDetectionRecovery] Stuck detection and recovery threads started, waiting for activation");
}

void StuckDetectionRecovery::Shutdown()
{
    LOG_INFO("[StuckDetectionRecovery] Shutting down stuck detection and recovery threads");

    // Stop threads
    detection_running_.store(false);
    recovery_running_.store(false);

    if (detection_thread_.joinable())
    {
        detection_thread_.join();
    }

    if (recovery_thread_.joinable())
    {
        recovery_thread_.join();
    }

    // Close data logging
    CloseDataLogging();
}

void StuckDetectionRecovery::StartDetection()
{
    bool was_active = detection_active_.exchange(true);
    if (!was_active)
    {
        LOG_INFO("[StuckDetectionRecovery] Activate stuck detection");

        // Clear history and restart detection
        std::lock_guard<std::mutex> lock(movement_mutex_);
        movement_history_.clear();
        is_stuck_.store(false);

        ResetSlidingWindows(); // Reset sliding windows
    }
}

void StuckDetectionRecovery::StopDetection()
{
    bool was_active = detection_active_.exchange(false);
    if (was_active)
    {
        LOG_INFO("[StuckDetectionRecovery] Pause stuck detection");

        std::lock_guard<std::mutex> lock(movement_mutex_);
        movement_history_.clear();
        is_stuck_.store(false);

        StopRecovery(); // Stop any ongoing recovery

        ResetSlidingWindows(); // Reset sliding windows
    }
}

bool StuckDetectionRecovery::IsDetectionActive() const
{
    return detection_active_.load();
}

void StuckDetectionRecovery::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_mutex_);
    latest_imu_data_ = imu_data;
    imu_data_valid_ = true;

    // Process IMU data
    ProcessImuData(imu_data);
}

void StuckDetectionRecovery::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_mutex_);
    latest_motor_data_ = motor_speed_data;
    motor_data_valid_ = true;

    // Process motor data
    ProcessMotorData(motor_speed_data);
}

bool StuckDetectionRecovery::IsStuck()
{
    return is_stuck_.load();
}

bool StuckDetectionRecovery::StartRecovery()
{
    if (recovery_active_.load())
    {
        LOG_WARN("[StuckDetectionRecovery] Recovery already in progress");
        return false;
    }

    LOG_INFO("[StuckDetectionRecovery] Start stuck recovery");
    recovery_active_.store(true);
    recovery_start_time_ = GetCurrentTimestamp();
    last_action_time_ = recovery_start_time_;
    recovery_cycle_count_ = 0;

    // Reset speed to initial values
    current_linear_speed_ = param_.initial_linear_speed;
    current_angular_speed_ = param_.initial_angular_speed;

    // Record recovery start position
    ResetMovementTracking();

    // Publish warning exception when recovery starts
    PublishException(fescue_iox::SocExceptionLevel::WARNING, fescue_iox::SocExceptionValue::ALG_PNC_STRONG_RECOVERY_MODE_ENABLED_WARNING);

    return true;
}

void StuckDetectionRecovery::StopRecovery()
{
    if (!recovery_active_.load())
    {
        return;
    }

    LOG_INFO("[StuckDetectionRecovery] Stop stuck recovery");
    recovery_active_.store(false);
    current_recovery_mode_ = RecoveryMode::NONE;

    // Stop movement
    PublishVelocity(0.0f, 0.0f, 100);
}

bool StuckDetectionRecovery::IsRecoveryActive() const
{
    return recovery_active_.load();
}

void StuckDetectionRecovery::ResetAllStates()
{
    LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] Reset all states and variables");

    // Reset detection state
    is_stuck_.store(false);
    detection_results_.clear();

    // Reset recovery state
    // detection_active_.store(false);
    recovery_active_.store(false);
    current_recovery_mode_ = RecoveryMode::NONE;
    current_linear_speed_ = param_.initial_linear_speed;
    current_angular_speed_ = param_.initial_angular_speed;
    recovery_start_time_ = 0;
    last_action_time_ = 0;
    recovery_cycle_count_ = 0;

    // Reset movement data
    {
        std::lock_guard<std::mutex> lock(movement_mutex_);
        movement_history_.clear();
        current_movement_ = MovementData();
        recovery_start_position_ = MovementData();
    }

    // Reset sliding windows
    ResetSlidingWindows();

    // Reset IMU related state
    // is_first_imu_ = true;
    // is_bias_calibrated_ = false;
    // bias_z_ = 0.0f;
    // calibration_samples_.clear();
    // filter_initialized_ = false;
    // filtered_angular_velocity_ = 0.0f;

    LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] All states and variables reset");
}

bool StuckDetectionRecovery::IsInRecoverySuccessCooldown() const
{
    if (recovery_success_time_ == 0)
    {
        return false; // Never had a recovery success
    }

    uint64_t current_time = GetCurrentTimestamp();
    uint64_t elapsed_time = current_time - recovery_success_time_;

    return elapsed_time < RECOVERY_SUCCESS_COOLDOWN_MS;
}

void StuckDetectionRecovery::SetVelocityPublisher(std::shared_ptr<VelocityPublisher> vel_publisher)
{
    vel_publisher_ = vel_publisher;
}

void StuckDetectionRecovery::SetExceptionPublisher(std::function<void(fescue_iox::SocExceptionLevel, fescue_iox::SocExceptionValue)> exception_publisher)
{
    exception_publisher_ = exception_publisher;
}

void StuckDetectionRecovery::SetParam(const StuckRecoveryParam &param)
{
    param_ = param;
}

StuckRecoveryParam StuckDetectionRecovery::GetParam() const
{
    return param_;
}

RecoveryMode StuckDetectionRecovery::GetCurrentRecoveryMode() const
{
    return current_recovery_mode_;
}

std::vector<WindowDetectionResult> StuckDetectionRecovery::GetDetectionResults() const
{
    return detection_results_;
}

void StuckDetectionRecovery::DetectionThread()
{
    LOG_INFO("[StuckDetectionRecovery] Detection thread started");

    while (detection_running_.load())
    {
        // Only detect when detection is active
        if (detection_active_.load())
        {
            // Update movement data
            UpdateMovementData();

            // Check if in recovery success cooldown
            if (IsInRecoverySuccessCooldown())
            {
                // During cooldown, do not perform stuck detection, ensure stuck state is false
                is_stuck_.store(false);
                LOG_INFO_THROTTLE(10000, "[StuckDetectionRecovery] In recovery success cooldown, pause stuck detection");
            }
            else
            {
                // Perform multi-window detection
                bool stuck_detected = IsStuckInMultipleWindows();

                // Update stuck state
                bool previous_stuck = is_stuck_.exchange(stuck_detected);

                if (stuck_detected && !previous_stuck)
                {
                    LOG_WARN("[StuckDetectionRecovery] Stuck state detected");
                }
                else if (!stuck_detected && previous_stuck)
                {
                    LOG_INFO("[StuckDetectionRecovery] Stuck state cleared");
                }
            }
        }
        else
        {
            // When detection is not active, ensure stuck state is false
            is_stuck_.store(false);
        }

        // Control detection frequency (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG_INFO("[StuckDetectionRecovery] Detection thread exited");
}

void StuckDetectionRecovery::RecoveryThread()
{
    LOG_INFO("[StuckDetectionRecovery] Recovery thread started");

    while (recovery_running_.load())
    {
        if (recovery_active_.load())
        {
            uint64_t current_time = GetCurrentTimestamp();

            // Check if exceeded max recovery time
            if (current_time - recovery_start_time_ > param_.max_recovery_duration_ms)
            {
                LOG_ERROR("[StuckDetectionRecovery] Recovery timeout, stopping recovery and mower operation");

                // Publish error exception when recovery fails/times out
                PublishException(fescue_iox::SocExceptionLevel::ERROR, fescue_iox::SocExceptionValue::ALG_PNC_STRONG_RECOVERY_MODE_FAILED_ERROR);

                // Stop recovery
                StopRecovery();

                // Stop mower operation immediately by publishing zero velocity for extended duration
                PublishVelocity(0.0f, 0.0f, 5000); // Stop for 5 seconds to ensure mower stops

                continue;
            }

            // Check for movement during recovery
            if (HasMovementDuringRecovery())
            {
                LOG_INFO("[StuckDetectionRecovery] Movement detected, recovery successful");

                // Record recovery success time, start 1 minute cooldown
                recovery_success_time_ = GetCurrentTimestamp();
                LOG_INFO("[StuckDetectionRecovery] Recovery successful, start 1 minute cooldown, stuck detection will be paused during this period");

                // Stop recovery
                StopRecovery();

                // Reset all states and variables
                ResetAllStates();

                continue;
            }

            // Perform recovery action
            if (current_time - last_action_time_ > param_.recovery_action_duration_ms)
            {
                // Switch to next recovery mode
                current_recovery_mode_ = GetNextRecoveryMode();

                // Progressive speed adjustment
                ProgressiveSpeedAdjustment();

                // Execute recovery action
                ExecuteRecoveryAction(current_recovery_mode_, current_linear_speed_, current_angular_speed_);

                last_action_time_ = current_time;
                recovery_cycle_count_++;

                LOG_INFO("[StuckDetectionRecovery] Execute recovery action: mode={}, linear_speed={:.2f}, angular_speed={:.2f}",
                         static_cast<int>(current_recovery_mode_), current_linear_speed_, current_angular_speed_);
            }
            else
            {
                // Execute recovery action
                ExecuteRecoveryAction(current_recovery_mode_, current_linear_speed_, current_angular_speed_);
            }
        }

        // Control recovery frequency (50Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG_INFO("[StuckDetectionRecovery] Recovery thread exited");
}

void StuckDetectionRecovery::ProcessImuData(const ImuData &imu_data)
{
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] Ignore first IMU data");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU bias calibration
    if (!is_bias_calibrated_)
    {
        // Ignore first second of data for calibration
        if (dt > 0.0f && calibration_samples_.size() < CALIBRATION_SAMPLES)
        {
            calibration_samples_.push_back(imu_data.angular_velocity_z);
        }

        if (calibration_samples_.size() >= CALIBRATION_SAMPLES)
        {
            // Calculate angular velocity bias
            float sum_angular = 0.0f;
            for (float sample : calibration_samples_)
            {
                sum_angular += sample;
            }
            bias_z_ = sum_angular / calibration_samples_.size();

            is_bias_calibrated_ = true;

            LOG_INFO("[StuckDetectionRecovery] IMU bias calibration complete: bias_z = {:.4f}", bias_z_);
        }

        return;
    }

    // Apply bias correction - angular velocity
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;

    // Save raw data for logging
    float raw_angular_velocity = angular_velocity_z;

    // Initialize filter (only on first use)
    if (!filter_initialized_)
    {
        InitializeFilters(angular_velocity_z);
        filter_initialized_ = true;
    }

    // Apply low-pass filter
    angular_velocity_z = ApplyLowPassFilter(angular_velocity_z, filtered_angular_velocity_, filter_alpha_);

    // Log pre- and post-filter data
    if (param_.enable_data_logging)
    {
        LogFilteringData(imu_data.system_timestamp, raw_angular_velocity, angular_velocity_z);
    }

    // Apply threshold filter - angular velocity
    if (std::abs(angular_velocity_z) < angular_velocity_z_threshold_)
    {
        angular_velocity_z = 0.0f;
    }

    // Update current movement data
    std::lock_guard<std::mutex> lock(movement_mutex_);
    current_movement_.angular_velocity = angular_velocity_z;
    // Calculate angular displacement for this frame (accumulated value, as this thread runs at 10ms intervals, other threads at 100ms)
    current_movement_.angular_displacement = std::abs(angular_velocity_z) * dt;
    current_movement_.timestamp = imu_data.system_timestamp;
}

void StuckDetectionRecovery::ProcessMotorData(const MotorSpeedData &motor_data)
{
    // Simplified: only log received motor data
    LOG_INFO_THROTTLE(5000, "[StuckDetectionRecovery] Received motor data: left={:.1f}RPM, right={:.1f}RPM",
                      motor_data.motor_speed_left, motor_data.motor_speed_right);
}

void StuckDetectionRecovery::UpdateMovementData()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    uint64_t current_time = GetCurrentTimestamp();

    // If new movement data, add to history and sliding window
    if (current_movement_.timestamp > 0)
    {
        MovementData data = current_movement_;
        data.timestamp = current_time;

        movement_history_.push_back(data);

        // Update sliding windows
        UpdateSlidingWindows(data);

        // Log data
        if (param_.enable_data_logging)
        {
            LogData(data);
        }

        // Reset current movement data
        current_movement_ = MovementData();
    }

    // Use general sliding window cleanup, keep data for the longest window (15min + 1min buffer)
    CleanupMovementHistory(16 * 60 * 1000); // 16 minutes
}

WindowDetectionResult StuckDetectionRecovery::CheckWindow(uint64_t window_duration_ms, float min_rotation)
{
    WindowDetectionResult result;
    result.window_duration_ms = window_duration_ms;

    uint64_t current_time = GetCurrentTimestamp();

    // Use general sliding window function to get window data
    std::deque<MovementData> window_data = GetWindowData(window_duration_ms, current_time);

    // Check data validity: need enough history for detection
    if (window_data.empty())
    {
        LOG_INFO("[StuckDetectionRecovery] Window {}ms movement data empty, skip detection", window_duration_ms);
        result.is_stuck = false; // Not stuck if data insufficient
        result.data_insufficient = true;
        return result;
    }

    // Check if data coverage is sufficient
    uint64_t earliest_timestamp = window_data.front().timestamp;
    uint64_t latest_timestamp = window_data.back().timestamp;
    uint64_t data_coverage_duration = latest_timestamp - earliest_timestamp;

    // If data coverage is less than 80% of window, consider insufficient
    uint64_t min_coverage_duration = window_duration_ms * 0.8;
    if (data_coverage_duration < min_coverage_duration)
    {
        LOG_INFO("[StuckDetectionRecovery] Window {}ms data coverage insufficient ({:.1f}s < {:.1f}s), skip detection",
                 window_duration_ms, data_coverage_duration / 1000.0f, min_coverage_duration / 1000.0f);
        result.is_stuck = false; // Not stuck if data insufficient
        result.data_insufficient = true;
        return result;
    }

    // Calculate total rotation in window
    int data_points_in_window = window_data.size();
    for (const auto &data : window_data)
    {
        result.total_rotation += std::abs(data.angular_displacement);
    }

    // Ensure enough data points in window (at least 10, i.e. 1 second of data)
    if (data_points_in_window < 10)
    {
        LOG_INFO("[StuckDetectionRecovery] Window {}ms has insufficient data points ({} < 10), skip detection",
                 window_duration_ms, data_points_in_window);
        result.is_stuck = false; // Not stuck if data insufficient
        result.data_insufficient = true;
        return result;
    }

    // Determine stuck state - based only on accumulated angle
    result.is_stuck = (result.total_rotation < min_rotation);
    result.data_insufficient = false;

    return result;
}

bool StuckDetectionRecovery::IsStuckInMultipleWindows()
{
    // Use independent sliding window detection
    std::vector<WindowDetectionResult> window_results;
    detection_results_.clear();

    uint64_t current_time = GetCurrentTimestamp();
    int valid_windows = 0;
    int stuck_windows = 0;

    // Check each sliding window
    {
        std::lock_guard<std::mutex> lock(sliding_windows_mutex_);

        for (size_t i = 0; i < sliding_windows_.size(); i++)
        {
            const auto &window = sliding_windows_[i];

            WindowDetectionResult result;
            result.window_duration_ms = window.window_duration_ms_;
            result.total_rotation = window.total_rotation_;

            // Check if data is sufficient
            if (window.HasSufficientData(current_time))
            {
                result.data_insufficient = false;
                result.is_stuck = window.IsStuck();
                valid_windows++;

                if (result.is_stuck)
                {
                    stuck_windows++;
                }
            }
            else
            {
                result.data_insufficient = true;
                result.is_stuck = false;
            }

            window_results.push_back(result);
            detection_results_.push_back(result);
        }
    }

    // Check if data is sufficient: at least 5min window must have enough data
    if (valid_windows == 0)
    {
        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] All windows have insufficient data, skip stuck detection");
        return false;
    }

    // If at least the specified number of windows detect stuck, consider stuck
    bool is_stuck = stuck_windows >= param_.min_stuck_windows;

    // Log window detection results to file
    if (param_.enable_data_logging)
    {
        LogWindowDetectionResults(window_results, is_stuck);
    }

    if (is_stuck)
    {
        LOG_WARN_THROTTLE(1000, "[StuckDetectionRecovery] Stuck detected (is_stuck=1): valid_windows={}, stuck_windows={}, min_stuck_windows={}",
                          valid_windows, stuck_windows, param_.min_stuck_windows);

        // Detailed log for each window
        std::vector<int> window_minutes = {5, 10, 15};
        std::vector<float> window_thresholds = {param_.rotation_threshold_5min, param_.rotation_threshold_10min, param_.rotation_threshold_15min};

        LOG_WARN_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[0], window_results[0].total_rotation, window_thresholds[0],
                          window_results[0].is_stuck ? "yes" : "no", window_results[0].data_insufficient ? "insufficient" : "sufficient");

        LOG_WARN_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[1], window_results[1].total_rotation, window_thresholds[1],
                          window_results[1].is_stuck ? "yes" : "no", window_results[1].data_insufficient ? "insufficient" : "sufficient");

        LOG_WARN_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[2], window_results[2].total_rotation, window_thresholds[2],
                          window_results[2].is_stuck ? "yes" : "no", window_results[2].data_insufficient ? "insufficient" : "sufficient");
    }
    else
    {
        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] Normal (is_stuck=0): valid_windows={}, stuck_windows={}, min_stuck_windows={}",
                          valid_windows, stuck_windows, param_.min_stuck_windows);

        // Detailed log for each window
        std::vector<int> window_minutes = {5, 10, 15};
        std::vector<float> window_thresholds = {param_.rotation_threshold_5min, param_.rotation_threshold_10min, param_.rotation_threshold_15min};

        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[0], window_results[0].total_rotation, window_thresholds[0],
                          window_results[0].is_stuck ? "yes" : "no", window_results[0].data_insufficient ? "insufficient" : "sufficient");

        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[1], window_results[1].total_rotation, window_thresholds[1],
                          window_results[1].is_stuck ? "yes" : "no", window_results[1].data_insufficient ? "insufficient" : "sufficient");

        LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] {}min window: total_rotation={:.3f}rad, threshold={:.3f}rad, stuck={}, data={}",
                          window_minutes[2], window_results[2].total_rotation, window_thresholds[2],
                          window_results[2].is_stuck ? "yes" : "no", window_results[2].data_insufficient ? "insufficient" : "sufficient");
    }

    return is_stuck;
}
void StuckDetectionRecovery::ExecuteRecoveryAction(RecoveryMode mode, float linear_speed, float angular_speed)
{
    switch (mode)
    {
    case RecoveryMode::ROTATE_LEFT:
        PublishVelocity(0.0f, angular_speed);
        break;

    case RecoveryMode::ROTATE_RIGHT:
        PublishVelocity(0.0f, -angular_speed);
        break;

    case RecoveryMode::FORWARD:
        PublishVelocity(linear_speed, 0.0f);
        break;

    case RecoveryMode::BACKWARD:
        PublishVelocity(-linear_speed, 0.0f);
        break;

    case RecoveryMode::SINGLE_WHEEL_LEFT:
    {
        // Left wheel rotates at high speed, right wheel slow or stationary
        float left_vel = 1.0;
        float right_vel = 0.0;
        float linear_speed = (left_vel + right_vel) / 2.0f;
        float angular_speed = (right_vel - left_vel) / param_.wheel_base;
        PublishVelocity(linear_speed, angular_speed);
        break;
    }

    case RecoveryMode::SINGLE_WHEEL_RIGHT:
    {
        // Right wheel rotates at high speed, left wheel slow or stationary
        float left_vel = 0.0;
        float right_vel = 1.0;
        float linear_speed = (left_vel + right_vel) / 2.0f;
        float angular_speed = (right_vel - left_vel) / param_.wheel_base;
        PublishVelocity(linear_speed, angular_speed);
        break;
    }

    case RecoveryMode::ALTERNATING_PUSH:
        // Alternate forward and backward
        if (recovery_cycle_count_ % 2 == 0)
        {
            PublishVelocity(linear_speed, 0.0f);
        }
        else
        {
            PublishVelocity(-linear_speed, 0.0f);
        }
        break;

    default:
        PublishVelocity(0.0f, 0.0f);
        break;
    }
}

RecoveryMode StuckDetectionRecovery::GetNextRecoveryMode()
{
    // Cycle through different recovery modes
    static const std::vector<RecoveryMode> recovery_modes = {
        RecoveryMode::ROTATE_LEFT,
        RecoveryMode::ROTATE_RIGHT,
        RecoveryMode::BACKWARD,
        RecoveryMode::FORWARD,
        RecoveryMode::SINGLE_WHEEL_LEFT,
        RecoveryMode::SINGLE_WHEEL_RIGHT,
        RecoveryMode::ALTERNATING_PUSH};

    int mode_index = recovery_cycle_count_ % recovery_modes.size();
    return recovery_modes[mode_index];
}

void StuckDetectionRecovery::ProgressiveSpeedAdjustment()
{
    // Increase speed every few cycles
    if (recovery_cycle_count_ > 0 && recovery_cycle_count_ % 3 == 0)
    {
        current_linear_speed_ = std::min(current_linear_speed_ + param_.speed_increment,
                                         param_.max_linear_speed);
        current_angular_speed_ = std::min(current_angular_speed_ + param_.speed_increment,
                                          param_.max_angular_speed);

        LOG_INFO("[StuckDetectionRecovery] Speed increased: linear_speed={:.2f}, angular_speed={:.2f}",
                 current_linear_speed_, current_angular_speed_);
    }
}

bool StuckDetectionRecovery::HasMovementDuringRecovery()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    // Calculate total rotation since recovery started
    float total_rotation = 0.0f;

    uint64_t recovery_start = recovery_start_time_;

    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= recovery_start)
        {
            total_rotation += std::abs(data.angular_displacement);
        }
    }

    return total_rotation > recovery_rotation_threshold_;
}

void StuckDetectionRecovery::ResetMovementTracking()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);
    recovery_start_position_ = MovementData();
    recovery_start_position_.timestamp = GetCurrentTimestamp();
}

void StuckDetectionRecovery::InitializeDataLogging()
{
    try
    {
        data_log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (data_log_file_.is_open())
        {
            // Write CSV header
            data_log_file_ << "timestamp,angular_displacement,angular_velocity\n";
            data_logging_initialized_ = true;
            LOG_INFO("[StuckDetectionRecovery] Data logging initialized: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] Unable to open data log file: {}", param_.log_file_path);
        }

        // Initialize filter data log file
        std::string filter_log_path = "/userdata/log/stuck_recovery_filter_data.csv";
        filter_log_file_.open(filter_log_path, std::ios::out | std::ios::trunc);
        if (filter_log_file_.is_open())
        {
            // Write CSV header
            filter_log_file_ << "timestamp,raw_angular_velocity,filtered_angular_velocity\n";
            LOG_INFO("[StuckDetectionRecovery] Filter data logging initialized: {}", filter_log_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] Unable to open filter data log file: {}", filter_log_path);
        }

        // Initialize window detection results log file
        std::string window_detection_log_path = "/userdata/log/stuck_window_detection_results.csv";
        window_detection_log_file_.open(window_detection_log_path, std::ios::out | std::ios::trunc);
        if (window_detection_log_file_.is_open())
        {
            // Write CSV header
            window_detection_log_file_ << "timestamp,window_5min_angle,window_5min_threshold,window_5min_stuck,window_5min_data_sufficient,"
                                       << "window_10min_angle,window_10min_threshold,window_10min_stuck,window_10min_data_sufficient,"
                                       << "window_15min_angle,window_15min_threshold,window_15min_stuck,window_15min_data_sufficient,"
                                       << "overall_stuck\n";
            LOG_INFO("[StuckDetectionRecovery] Window detection results logging initialized: {}", window_detection_log_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] Unable to open window detection results log file: {}", window_detection_log_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[StuckDetectionRecovery] Data logging initialization failed: {}", e.what());
    }
}

void StuckDetectionRecovery::LogData(const MovementData &data)
{
    if (data_logging_initialized_ && data_log_file_.is_open())
    {
        data_log_file_ << data.timestamp << ","
                       << data.angular_displacement << ","
                       << data.angular_velocity << "\n";
        data_log_file_.flush();
    }
}

void StuckDetectionRecovery::LogFilteringData(uint64_t timestamp, float raw_angular_vel, float filtered_angular_vel)
{
    if (data_logging_initialized_ && filter_log_file_.is_open())
    {
        filter_log_file_ << timestamp << ","
                         << raw_angular_vel << ","
                         << filtered_angular_vel << "\n";
        filter_log_file_.flush();
    }
}

void StuckDetectionRecovery::LogWindowDetectionResults(const std::vector<WindowDetectionResult> &results, bool overall_stuck)
{
    if (data_logging_initialized_ && window_detection_log_file_.is_open())
    {
        uint64_t timestamp = GetCurrentTimestamp();

        // Ensure there are 3 window results (5min, 10min, 15min)
        std::vector<float> thresholds = {param_.rotation_threshold_5min, param_.rotation_threshold_10min, param_.rotation_threshold_15min};

        window_detection_log_file_ << timestamp;

        // Log each window's data
        for (size_t i = 0; i < 3; i++)
        {
            if (i < results.size())
            {
                const auto &result = results[i];
                window_detection_log_file_ << "," << result.total_rotation
                                           << "," << thresholds[i]
                                           << "," << (result.is_stuck ? 1 : 0)
                                           << "," << (result.data_insufficient ? 0 : 1);
            }
            else
            {
                // If not enough results, fill with default values
                window_detection_log_file_ << ",0.0," << thresholds[i] << ",0,0";
            }
        }

        // Log overall stuck state
        window_detection_log_file_ << "," << (overall_stuck ? 1 : 0) << "\n";
        window_detection_log_file_.flush();
    }
}

void StuckDetectionRecovery::CloseDataLogging()
{
    if (data_log_file_.is_open())
    {
        data_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] Data logging closed");
    }
    if (filter_log_file_.is_open())
    {
        filter_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] Filter data logging closed");
    }
    if (window_detection_log_file_.is_open())
    {
        window_detection_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] Window detection results logging closed");
    }
    data_logging_initialized_ = false;
}

uint64_t StuckDetectionRecovery::GetCurrentTimestamp() const
{
    return GetTimestampMs();
}

float StuckDetectionRecovery::CalculateRotation(float angular_vel, float dt)
{
    return std::abs(angular_vel) * dt;
}

void StuckDetectionRecovery::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
    }
}

float StuckDetectionRecovery::ApplyLowPassFilter(float new_value, float &filtered_value, float alpha)
{
    // First-order low-pass filter: filtered_value = alpha * new_value + (1 - alpha) * filtered_value
    // The smaller alpha is, the stronger the filtering (smoother but slower response)
    filtered_value = alpha * new_value + (1.0f - alpha) * filtered_value;
    return filtered_value;
}

void StuckDetectionRecovery::InitializeFilters(float initial_angular_velocity)
{
    filtered_angular_velocity_ = initial_angular_velocity;
    LOG_INFO("[StuckDetectionRecovery] Filter initialized: angular_velocity={:.4f}", initial_angular_velocity);
}

void StuckDetectionRecovery::CleanupMovementHistory(uint64_t max_retention_time_ms)
{
    // Note: This function assumes the caller already holds the movement_mutex_ lock
    uint64_t current_time = GetCurrentTimestamp();
    uint64_t cutoff_time = current_time - max_retention_time_ms;

    // Clean up historical data exceeding retention time
    while (!movement_history_.empty() && movement_history_.front().timestamp < cutoff_time)
    {
        movement_history_.pop_front();
    }
}

std::deque<MovementData> StuckDetectionRecovery::GetWindowData(uint64_t window_duration_ms, uint64_t current_time)
{
    std::deque<MovementData> window_data;
    uint64_t window_start_time = current_time - window_duration_ms;

    std::lock_guard<std::mutex> lock(movement_mutex_);

    // Extract data from history within the specified time window
    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= window_start_time && data.timestamp <= current_time)
        {
            window_data.push_back(data);
        }
    }

    return window_data;
}

void StuckDetectionRecovery::InitializeSlidingWindows()
{
    std::lock_guard<std::mutex> lock(sliding_windows_mutex_);

    sliding_windows_.clear();

    // Create sliding windows for 5min, 10min, 15min
    sliding_windows_.emplace_back(5 * 60 * 1000, param_.rotation_threshold_5min);   // 5min
    sliding_windows_.emplace_back(10 * 60 * 1000, param_.rotation_threshold_10min); // 10min
    sliding_windows_.emplace_back(15 * 60 * 1000, param_.rotation_threshold_15min); // 15min

    LOG_INFO("[StuckDetectionRecovery] Sliding windows initialized: 5min(threshold={:.3f}), 10min(threshold={:.3f}), 15min(threshold={:.3f})",
             param_.rotation_threshold_5min, param_.rotation_threshold_10min, param_.rotation_threshold_15min);
}

void StuckDetectionRecovery::UpdateSlidingWindows(const MovementData &new_data)
{
    std::lock_guard<std::mutex> lock(sliding_windows_mutex_);

    // Update all sliding windows
    for (auto &window : sliding_windows_)
    {
        window.AddData(new_data);
    }
}

void StuckDetectionRecovery::ResetSlidingWindows()
{
    std::lock_guard<std::mutex> lock(sliding_windows_mutex_);

    // Reset all sliding windows
    for (auto &window : sliding_windows_)
    {
        window.Reset();
    }

    LOG_INFO_THROTTLE(1000, "[StuckDetectionRecovery] All sliding windows reset");
}

void StuckDetectionRecovery::PublishException(SocExceptionLevel level, SocExceptionValue value)
{
    if (exception_publisher_)
    {
        exception_publisher_(level, value);
        LOG_INFO("[StuckDetectionRecovery] Published exception: level={}, value={}",
                 static_cast<int>(level), static_cast<int>(value));
    }
    else
    {
        LOG_WARN("[StuckDetectionRecovery] Exception publisher not set, cannot publish exception: level={}, value={}",
                 static_cast<int>(level), static_cast<int>(value));
    }
}

} // namespace fescue_iox
