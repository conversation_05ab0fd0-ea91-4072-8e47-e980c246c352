#include "mower.hpp"

#include "mower_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <utility>

#define TEST 0

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationMowerAlg::NavigationMowerAlg(const MowerAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("MowerAlg"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    last_grass_time_ = std::chrono::steady_clock::now();
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    last_zero_velocity_time_ = std::chrono::steady_clock::now();
    exception_start_time_ = std::chrono::steady_clock::now();

    beacon_status_ = BeaconStatus(-1, 0);

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    SetMowerAlgParam(param);
    InitPublisher();
    InitSlipDetection();
    InitStuckDetectionRecovery();
}

NavigationMowerAlg::~NavigationMowerAlg()
{
    DeinitSlipDetection();
    DeinitStuckDetectionRecovery();
    LOG_WARN("NavigationMowerAlg exit!");
}

void NavigationMowerAlg::InitSlipDetection()
{
    slip_detection_running_.store(true);
    slip_detection_thread_ = std::thread(&NavigationMowerAlg::SlipDetectionThread, this);
}

void NavigationMowerAlg::DeinitSlipDetection()
{
    slip_detection_running_.store(false);
    if (slip_detection_thread_.joinable())
    {
        slip_detection_thread_.join();
    }
}

void NavigationMowerAlg::SlipDetectionThread()
{
    while (slip_detection_running_.load())
    {
        // Get the latest motor speed and motion detection data
        MotorSpeedData motor_speed_data;
        MotionDetectionResult motion_detection_result;

        {
            std::lock_guard<std::mutex> lock(motor_speed_mtx_);
            motor_speed_data = motor_speed_data_;
        }

        {
            std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
            motion_detection_result = motion_detection_result_;
        }

        // Perform slip detection
        bool current_slip = IsWheelSlipping(motor_speed_data, motion_detection_result, wheel_radius_, wheel_base_);
        LOG_INFO_THROTTLE(5000, "[MowerAlg] [SlipDetectionThread] current_slip({})", current_slip);

        if (current_slip)
        {
            LOG_INFO_THROTTLE(1000, "[MowerAlg] [SlipDetectionThread] slip detected!");

            if (!is_slipping_detected_.exchange(true)) // First time slip detected, record slip start time
            {
                slip_start_time_ = std::chrono::steady_clock::now();
            }

            // SetSlippingStatus(true); // Set slip status

            auto slip_duration = std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::steady_clock::now() - slip_start_time_);

            if (slip_duration.count() >= 10)
            {
                LOG_WARN_THROTTLE(1000, "[MowerAlg] [SlipDetectionThread] (WARNING) slip detected for {} seconds!", slip_duration.count());

                PublishSlipException(SocExceptionLevel::WARNING,
                                     SocExceptionValue::ALG_PNC_MOWING_SLIPPING_EXCEPTION);
            }
        }
        else
        {
            LOG_INFO_THROTTLE(5000, "[MowerAlg] [SlipDetectionThread] slip not detected!");

            if (is_slipping_detected_.exchange(false)) // Recovered from slip
            {
                // SetSlippingStatus(false); // Clear slip status
            }

            // SetSlippingStatus(false); // Clear slip status
        }

        // Control detection frequency
        // std::this_thread::sleep_for(std::chrono::milliseconds(1000 / slip_detection_frequency_));
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }
}

void NavigationMowerAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;

    // Pass motor data to stuck detection system
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetMotorSpeedData(motor_speed_data);
    }

    // LOG_INFO_THROTTLE(1000, "[MowerAlg] [SetMotorSpeedData] motor_speed_left({}), motor_speed_right({})",
    //           motor_speed_data.motor_speed_left, motor_speed_data.motor_speed_right);
}

void NavigationMowerAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
    // LOG_INFO_THROTTLE(1000, "[MowerAlg] [SetMotionDetectionResult] is_motion({})", motion_detection_result.is_motion);
}

void NavigationMowerAlg::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_data_mtx_);
    imu_data_ = imu_data;

    // Pass IMU data to stuck detection system
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetImuData(imu_data);
    }
}

bool NavigationMowerAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                         const MotionDetectionResult &motion_detection_result,
                                         float wheel_radius, float wheel_base)
{
    if (motor_data.system_timestamp - last_imu_timestamp_ < 0)
    {
        LOG_ERROR("[MowerAlg] [IsWheelSlipping1] 时间戳差值小于0");
        return false;
    }

    last_imu_timestamp_ = motor_data.system_timestamp;
    motion_detection_timestamp_ = motion_detection_result.timestamp;

    static bool is_slipping = false; // Track current slip status
    static int slip_counter = 0;
    const int slip_threshold = 200;

    // Convert motor speed from RPM to rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] w_left({}), w_right({})", w_left, w_right);

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    float act_linear = (v_right + v_left) / 2.0f;
    float act_angular = (v_right - v_left) / wheel_base;

    // bool is_data_synced =
    //     std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)) < 1000; // ms

    // if (!is_data_synced)
    // {
    //     LOG_ERROR("[MowerAlg] [IsWheelSlipping1] Time sync check failed");
    //     return false;
    // }

    bool is_has_speed = (fabs(act_linear) > min_valid_linear_) || (fabs(act_angular) > min_valid_angular_);
    bool potential_slip = is_has_speed && !motion_detection_result.is_motion;

    if (!is_slipping)
    {
        if (potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = true;
            slip_counter = 0;
            LOG_INFO_THROTTLE(1000, "[MowerAlg] [IsWheelSlipping1] Slipping status detected");
        }
    }
    else // is_slipping == true
    {
        if (!potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = false;
            slip_counter = 0;
            LOG_INFO_THROTTLE(1000, "[MowerAlg] [IsWheelSlipping1] Slipping status ended");
        }
    }

    LOG_INFO_THROTTLE(5000, "[MowerAlg] [IsWheelSlipping1] act_linear({}), act_angular({}), is_motion({}), is_slipping({}), slip_counter({})",
                      act_linear, act_angular, motion_detection_result.is_motion, is_slipping, slip_counter);

    return is_slipping;
}

void NavigationMowerAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

const char *NavigationMowerAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationMowerAlg::SetChargePileDockStatus(bool status)
{
    // LOG_INFO_THROTTLE(1000, "[BeaconDetection] charge_pile_dock_status({})", status);
}

void NavigationMowerAlg::SetMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    is_power_connected_ = data.charge_terminal_status;
    // LOG_INFO_THROTTLE(1000, "[BeaconDetection] charge_terminal_status({})", is_power_connected_);
}

void NavigationMowerAlg::SetMowerComplete(const bool &mower_completed)
{
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] [Run] Mower complete!");
    mower_completed_ = mower_completed;
}

void NavigationMowerAlg::SetGrassDetecteStatus(const GrassDetectStatus &data)
{
    grass_detect_status_ = data;
    LOG_DEBUG("[SetGrassDetecteStatus] grass_detect_status_({})", int(grass_detect_status_));

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (IsGrassField(grass_detect_status_)) // On grass field
        {
            is_on_grass_field_ = true;
            LOG_DEBUG("[SetGrassDetecteStatus] Is on grass field!");

            is_first_non_grass_detection_ = true; // Reset non-grass timer
            last_grass_time_ = std::chrono::steady_clock::now();
        }
        else // Not on grass field
        {
            is_on_grass_field_ = false;
            LOG_DEBUG("[SetGrassDetecteStatus] Is not on grass field!");

            auto current_time = std::chrono::steady_clock::now();
            if (is_first_non_grass_detection_)
            {
                last_grass_time_ = current_time;
                is_first_non_grass_detection_ = false;
            }
            non_grass_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_grass_time_);
            LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus] Non-grass duration: {} seconds", non_grass_duration_.count());
        }
    }
}

void NavigationMowerAlg::SetAllTaskClose()
{
    thread_control_ = ThreadControl::CLOSE_ALL_TASK;
    UpdateFeatureSelection(thread_control_);
    ResetMowerAlgFlags();
}

void NavigationMowerAlg::TestNonBlockingControl()
{
    if (!is_test_running_)
    {
        // Initialize test
        is_test_running_ = true;
        test_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[TestControl] Start non-blocking velocity control test");
    }

    // Calculate elapsed time
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                       std::chrono::steady_clock::now() - test_start_time_)
                       .count();

    if (elapsed < test_duration_ms_)
    {
        // Continuously send velocity commands (non-blocking)
        PublishVelocity(test_linear_speed_, test_angular_speed_, 0.0);
        LOG_INFO_THROTTLE(500, "[TestControl] Controlling... Remaining time: {} ms",
                          test_duration_ms_ - elapsed);
    }
    else
    {
        // Stop motion at the end of the test
        PublishVelocity(0.0, 0.0, 0.0);
        // is_test_running_ = false;
        LOG_INFO("[TestControl] Test completed, total time {} ms", test_duration_ms_);
    }
}

void NavigationMowerAlg::Test(CrossRegionRunningState cross_region_state)
{
    TestNonBlockingControl();
#if 0
    //! 1. First stage. Start spiral mowing function
    if (!mcu_triggers_cross_region_ &&
        !mcu_triggers_mower_ &&
        mcu_triggers_spiral_mower_)
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start spiral mowing mode!");
        thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    //! 2. Second stage. Start random mowing function. MCU does not issue cross-region or recharge request
    if (!mcu_triggers_cross_region_ && /* MCU no cross-region request */
        mcu_triggers_mower_)           /* MCU mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start mowing mode!");
        PerformRandomMowing();
    }

    //! 3. Third stage. MCU issues cross-region request
    if (mcu_triggers_cross_region_ && /* MCU cross-region request */
        !mcu_triggers_mower_)         /* MCU no mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start cross-region mode!");

        // Execute cross-channel thread, close edge-follow thread
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

#endif
}

MowerAlgResult NavigationMowerAlg::Run(const MarkLocationResult &mark_loc_result,
                                       CrossRegionRunningState cross_region_state,
                                       const QRCodeLocationResult &qrcode_loc_result,
                                       const PerceptionFusionResult &fusion_result,
                                       RechargeRunningState recharge_state,
                                       McuExceptionStatus &mcu_exception_status,
                                       BehaviorRunningState &behavior_state,
                                       RandomMowerRunningState &random_mower_state)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        ShowMowerRunningInfo(mark_loc_result);
        LOG_WARN_THROTTLE(3000, "Mower Alg Run() is PAUSE!");

        SetStuckDetectionActive(false);
        SetResetAllStuckStates();

        return MowerAlgResult(false);
    }

    if (mower_completed_)
    {
        return MowerAlgResult(true);
    }

#if (TEST == 1)

    ShowMowerRunningInfo(mark_loc_result);

    if (mcu_triggers_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_spiral_mower_)
    {
        Test(cross_region_state);
    }
    else
    {
        is_test_running_ = false;
    }
#else

    random_mower_state_ = random_mower_state;

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_region_explore_mode_start_)
        {
            RegionExploreResult region_explore_result;
            {
                region_explore_result.result = true;
                region_explore_result.timestamp = GetTimestampMs();
                region_explore_result.master_region_map_result = master_region_explore_result_;
                region_explore_result.slave_region_map_result = slave_region_explore_result_;
            }

            if (region_explore_result_callback_)
            {
                region_explore_result_callback_(region_explore_result);
            }

            is_region_explore_mode_start_ = false;
        }
    }

    if (mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_cut_border_mode_start_)
        {
            if (cut_border_result_callback_)
            {
                cut_border_result_callback_(true, true);
            }

            is_cut_border_mode_start_ = false;
        }
    }

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        HandleSelfCheckAndOperation(mark_loc_result, cross_region_state,
                                    qrcode_loc_result, fusion_result, recharge_state,
                                    mcu_exception_status, behavior_state);
    }

#endif

    return MowerAlgResult(mower_completed_);
}

//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::HandleSelfCheckAndOperation(const MarkLocationResult &mark_loc_result,
                                                     CrossRegionRunningState cross_region_state,
                                                     const QRCodeLocationResult &qrcode_loc_result,
                                                     const PerceptionFusionResult &fusion_result,
                                                     RechargeRunningState recharge_state,
                                                     McuExceptionStatus &mcu_exception_status,
                                                     BehaviorRunningState &behavior_state)
{
    if (!is_self_checking_)
    {
        is_self_checking_ = true;
        is_self_recovery_active_ = false;
        self_check_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[BeaconDetection] Start self-check...");

        SetUndockResult(false, false, mower_msgs::srv::UndockOperationStatus::NOT_INTERRUPTIBLE);

        if (area_calc_start_callback_)
        {
            area_calc_start_callback_(GetTimestampMs());
        }
    }
    else
    {
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - self_check_start_time_);

        if (is_self_recovery_active_)
        {
            LOG_INFO("[BeaconDetection] Self-check recovery in progress...");
            return;
        }
        else if (duration.count() >= SELF_CHECK_DURATION_SECONDS)
        {
            if (is_self_success_)
            {
                NormalOperation(mark_loc_result, cross_region_state,
                                qrcode_loc_result, fusion_result, recharge_state,
                                mcu_exception_status, behavior_state);
            }
            else
            {
                LOG_WARN("[BeaconDetection] Initial self-check failed, starting self-check recovery process");

                is_self_recovery_active_ = true;

                std::thread recovery_thread([this]() {
                    bool recovery_result = PerformSelfCheckRecovery();

                    is_self_recovery_active_ = false;
                    is_self_success_ = recovery_result;

                    if (recovery_result)
                    {
                        LOG_INFO("[BeaconDetection] Self-check recovery succeeded, will continue normal operation");
                    }
                    else
                    {
                        LOG_WARN("[BeaconDetection] Self-check recovery failed, will not perform normal operation");
                    }
                });

                recovery_thread.detach();
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection] Self-check in progress, remaining time: {} seconds", SELF_CHECK_DURATION_SECONDS - duration.count());
            is_self_success_ = SelfChecking();
        }
    }
}
bool NavigationMowerAlg::SelfChecking()
{
    // 1. Check for abnormal state when not charging
    // 2. Check for abnormal state when not on grass
    if (!is_power_connected_ &&
        !is_on_grass_field_ && non_grass_duration_.count() >= SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS)
    {
        LOG_WARN("[BeaconDetection] Detected non-grass for more than {} seconds during self-check", SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS);

        return false;
    }

    return true;
}

bool NavigationMowerAlg::PerformSelfCheckRecovery()
{
    uint64_t recovery_duration_ms = static_cast<uint64_t>(1000.0 * self_recovery_distance_ / std::abs(self_recovery_linear_speed_));

    LOG_INFO("[BeaconDetection] Self-check failed, start reversing {} meters, expected duration {} ms",
             self_recovery_distance_, recovery_duration_ms);

    PublishVelocity(self_recovery_linear_speed_, 0.0);

    auto recovery_start_time = std::chrono::steady_clock::now();

    auto last_check_time = std::chrono::steady_clock::now();

    while (true)
    {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - recovery_start_time).count();
        LOG_INFO("[BeaconDetection] Self-check in progress, reversed {} ms", elapsed_ms);

        PublishVelocity(self_recovery_linear_speed_, 0.0);

        if (elapsed_ms >= recovery_duration_ms)
        {
            PublishVelocity(0.0, 0.0, 1000);
            LOG_INFO("[BeaconDetection] Reverse completed, total reversed {} ms", elapsed_ms);

            if (!SelfChecking())
            {
                LOG_WARN("[BeaconDetection] Self-check still failed after reversing, reporting exception");
                SetAllTaskClose();

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_SELF_CHECK_FAILED_EXCEPTION);
                return false;
            }
            else
            {
                LOG_INFO("[BeaconDetection] Self-check succeeded after reversing");
                return true;
            }
        }

        if (std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - last_check_time)
                .count() >= 100)
        {
            last_check_time = current_time;

            if (SelfChecking())
            {
                PublishVelocity(0.0, 0.0, 1000);
                LOG_INFO("[BeaconDetection] Self-check succeeded during reversing, reversed {} ms", elapsed_ms);
                return true;
            }
            else
            {
                LOG_WARN("[BeaconDetection] Self-check failed during reversing, reversed {} ms", elapsed_ms);
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    return false;
}
//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::NormalOperation(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state)
{
    ShowExplorePrint(behavior_state);
    ShowMowerRunningInfo(mark_loc_result);

    SetStuckDetectionActive(true);

    if (mcu_triggers_cut_border_)
    {
        is_cut_border_mode_start_ = true;
        CutBorderModule(mark_loc_result, cross_region_state,
                        qrcode_loc_result, fusion_result, recharge_state,
                        mcu_exception_status, behavior_state);
    }
    else
    {
        if (mcu_triggers_region_exploration_) // Region exploration
        {
            is_region_explore_mode_start_ = true;
            RegionExplorationModule(mark_loc_result, cross_region_state,
                                    qrcode_loc_result, fusion_result, recharge_state,
                                    mcu_exception_status, behavior_state);
        }
        else // Normal operation logic
        {
            NormalMowingModule(mark_loc_result, cross_region_state,
                               qrcode_loc_result, fusion_result, recharge_state,
                               mcu_exception_status, behavior_state);
        }
    }
}

void NavigationMowerAlg::CutBorderModule(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state)
{
    // Step 1, un-dock
    // ProcessCutBorderUnstakeMode();
    ProcessCutBorderUnstakeMode(qrcode_loc_result);

    // Initialize only on first entry
    if (is_first_enter_cut_border_last_qr_detection_time_)
    {
        last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now(); // After un-docking, start QR code detection timing
        is_first_enter_cut_border_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[CutBorderModule] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[CutBorderModule] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[CutBorderModule] Stuck recovery in progress...");
        return;
    }
    if (is_slipping_detected_.load())
    {
        HandleCutBorderMcuException(recharge_state, cross_region_state);
        LOG_INFO_THROTTLE(1000, "[CutBorderModule1] Slipping detected, entering exception handling");
    }
    else
    {
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleCutBorderMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // Cut border function
            PerformCutBorder(mark_loc_result, qrcode_loc_result, cross_region_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::RegionExplorationModule(const MarkLocationResult &mark_loc_result,
                                                 CrossRegionRunningState cross_region_state,
                                                 const QRCodeLocationResult &qrcode_loc_result,
                                                 const PerceptionFusionResult &fusion_result,
                                                 RechargeRunningState recharge_state,
                                                 McuExceptionStatus &mcu_exception_status,
                                                 BehaviorRunningState &behavior_state)
{
    // Step 1, un-dock. No need to handle MCU exception state
    // ProcessExplorationUnstakeMode();
    ProcessExplorationUnstakeMode(qrcode_loc_result);

    // Initialize only on first entry
    if (is_first_enter_explore_last_qr_detection_time_)
    {
        last_qr_explore_detection_time_ = std::chrono::steady_clock::now(); // After un-docking, start QR code detection timing
        is_first_enter_explore_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[RegionExplorationModule1] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] Stuck recovery in progress...");
        return;
    }

    if (is_slipping_detected_.load())
    {
        HandleExplorationMcuException(recharge_state, cross_region_state);
        LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] Slipping detected, entering exception handling");
    }
    else
    {
        // Handle different MCU exception states
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleExplorationMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // Region exploration function
            PerformExploration(mark_loc_result, qrcode_loc_result, cross_region_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::NormalMowingModule(const MarkLocationResult &mark_loc_result,
                                            CrossRegionRunningState cross_region_state,
                                            const QRCodeLocationResult &qrcode_loc_result,
                                            const PerceptionFusionResult &fusion_result,
                                            RechargeRunningState recharge_state,
                                            McuExceptionStatus &mcu_exception_status,
                                            BehaviorRunningState &behavior_state)
{
    if (is_power_connected_) // Connected to power
    {
        // ProcessNormalOperationUnstakeMode();
        ProcessNormalOperationUnstakeMode(qrcode_loc_result);
    }
    else // Not connected to power
    {
        if (!is_unstaking_)
        {
            PreProcessingMowing(qrcode_loc_result);
        }
    }

    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive()) // IsStuckDetected must be true, IsStuckRecoveryActive must be false
    {
        LOG_WARN("[NormalMowingModule1] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive()) // IsStuckDetected can be false or true, IsStuckRecoveryActive must be true
    {
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] Stuck recovery in progress...");
        return;
    }

    if (is_slipping_detected_.load())
    {
        HandleMcuException(recharge_state, cross_region_state);
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] Slipping detected, entering exception handling");
    }
    else
    {
        // Handle different MCU exception states
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleMcuException(recharge_state, cross_region_state);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            HandleNormalCrossRegionStates(cross_region_state);

            HandleNormalOperation(cross_region_state, recharge_state, behavior_state);
            break;
        }
        default:
            break;
        }
    }
}

//==============================================================================
// Region exploration function handlers
//==============================================================================
void NavigationMowerAlg::PerformExploration(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                            CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In edge-following or undefined mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In edge-following or undefined mode");

        bool is_beacon_valid = false; // Default: beacon invalid
        ProcessBeaconDetection(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaExplorationMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaExplorationMode(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In cross-region mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In cross-region mode");

        // Handle different cross-region states
        HandleExploreCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In recharge mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In recharge mode");

        ProcessingExplorationRecharge(qrcode_loc_result);

        if (is_first_region_explore_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_region_explore_mode_end_ = false;
        }
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");

        break;
    }
}

/**
 * @brief Continuously detect beacon state
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessBeaconDetection(const MarkLocationResult &mark_loc_result,
                                                bool &enter_multi_region_exploration,
                                                bool &is_beacon_valid)
{
    if (!is_enable_unstake_mode_ || is_unstake_success_) /* Unstake mode not enabled or already successful (first stage complete) */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Start region exploration mode");

        if (mark_loc_result.mark_perception_status == 0) // Perception did not detect beacon
        {
            LOG_INFO_THROTTLE(1000, "[BeaconDetection] 1. Perception did not detect beacon, enable edge following");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
        }
        else // Perception detected beacon
        {
            LOG_INFO("[BeaconDetection] 2. Perception detected beacon");

            if (mark_loc_result.mark_id_distance.size() <= 0) // No value in mark_id_distance
            {
                LOG_INFO("[BeaconDetection] 2.1 No value in mark_id_distance from localization");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
            }
            else // mark_id_distance has value
            {
                LOG_INFO("[BeaconDetection] 2.2 mark_id_distance from localization has value");

                // Determine if beacon is valid (distance < 50cm is considered valid)
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // If beacon invalid, do nothing, continue previous action
                {
                    LOG_INFO("[BeaconDetection] 2.2.1 Cross-region beacon invalid");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, is_cooldown_active_);
                }
                else // If beacon valid, check stack container
                {
                    LOG_INFO("[BeaconDetection] 2.2.2 Cross-region beacon valid");
                    LOG_INFO("[BeaconDetection] 2.2.2 Valid beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    //! Beacon valid, can enter multi-region exploration
                    current_mark_id_ = mark_id_distance_vec[shortest_dis_inx].mark_id;
                    if (first_detection_beacon_)
                    {
                        beacon_status_ = BeaconStatus(current_mark_id_, 1);
                        first_detection_beacon_ = false;
                    }

                    enter_multi_region_exploration = true;
                    is_beacon_valid = true;
                }
            }
        }
    }
}
/**
 * @brief Use charging station QR code detection to determine when to stop edge following and switch to recharge
 *
 * @param qrcode_loc_result
 * @param enter_multi_region_exploration
 */
void NavigationMowerAlg::ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                                          const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !enter_multi_region_exploration)                     /* Not entering multi-region exploration */
    {
        // LOG_INFO("[BeaconDetection] Start single area exploration mode");
        LOG_INFO("[BeaconDetection] Start single area exploration mode");

        if (qrcode_loc_result.detect_status != QRCodeDetectStatus::NO_DETECT_QRCODE)
        {
            auto current_time = std::chrono::steady_clock::now();
            qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_explore_detection_time_);

            LOG_INFO("[BeaconDetection] Charging station QR code detection cooldown timer (seconds): ({})", qr_detection_duration_.count());
            if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // Increase timer
            {
                qr_code_detection_count_++;
                last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
                LOG_INFO("[BeaconDetection] Detected valid charging station QR code pose, current detection count: {}", qr_code_detection_count_);
            }
        }

        // Use QR code detection to determine when to stop edge following and switch to recharge
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection] Detected valid charging station QR code pose twice, switching to recharge mode");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // Reset state
            qr_code_detection_count_ = 1;

            // float area = 0.0;
            // float perimeter = 0.0;
            // if (area_calc_stop_callback_)
            // {
            //     area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
            // }

            // LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

            // {
            //     master_region_explore_result_.is_exist = true;
            //     master_region_explore_result_.area = area;
            //     master_region_explore_result_.perimeter = perimeter;
            //     master_region_explore_result_.charge_station_flag = true;
            //     master_region_explore_result_.beacon_id = -1;

            //     slave_region_explore_result_.is_exist = false;
            // }
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                                         const bool &enter_multi_region_exploration,
                                                         bool &is_beacon_valid)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        enter_multi_region_exploration)                      /* Entering multi-region exploration */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start multi-region exploration mode");

        if (is_beacon_valid) // Beacon is valid
        {
            // Reset cooldown timestamp and activate cooldown mechanism
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection] Current detected mark_id: {}", current_mark_id_);

            // Initialize only on first entry
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
                is_first_enter_last_mark_detection_time_ = false;
                LOG_INFO("[BeaconDetection] Beacon detection start timing");

                // stop
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
                }

                {
                    master_region_explore_result_.is_exist = false;
                    slave_region_explore_result_.is_exist = false;
                }

                // start
                if (area_calc_start_callback_)
                {
                    area_calc_start_callback_(GetTimestampMs());
                }
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection] Current timestamp (seconds) current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection] Last timestamp (seconds) last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_INFO("[BeaconDetection] Beacon detection cooldown timer (seconds): ({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // Same mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] Same mark_id, current detected mark_id: {}", current_mark_id_);
                }
                else // Different mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] Different mark_id, current detected mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection] Different mark_id, last detected mark_id: {}", beacon_status_.mark_id);
                }
            }
        }

        if (beacon_status_.beacon_look_count >= 2)
        {
            // Start cross-region process
            LOG_INFO("[BeaconDetection] Beacon detected more than twice, starting cross-region process");

            thread_control_ = ThreadControl::CROSS_REGION_THREAD;
            UpdateFeatureSelection(thread_control_);
            EdgeFollowDisable();
            is_single_area_recharge_ = false;

            // Reset state
            next_paired_beacon_id_ = PairNumber(current_mark_id_);
            beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
            LOG_INFO("[BeaconDetection] Next paired beacon id is {}", next_paired_beacon_id_);

            float area = 0.0;
            float perimeter = 0.0;
            if (area_calc_stop_callback_)
            {
                area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
            }

            LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

            if (is_master_region_) // Master region
            {
                {
                    master_region_explore_result_.is_exist = true;
                    master_region_explore_result_.area = area;
                    master_region_explore_result_.perimeter = perimeter;
                    master_region_explore_result_.charge_station_flag = true;
                    master_region_explore_result_.beacon_id = current_mark_id_;
                }

                is_master_region_ = false; // Next is slave region
            }
            else // Slave region
            {
                {
                    slave_region_explore_result_.is_exist = true;
                    slave_region_explore_result_.area = area;
                    slave_region_explore_result_.perimeter = perimeter;
                    slave_region_explore_result_.charge_station_flag = false;
                    slave_region_explore_result_.beacon_id = current_mark_id_;
                }
            }
        }
        else
        {
            // Continue edge following
            LOG_INFO_THROTTLE(2000, "[BeaconDetection] Beacon detection not more than twice, continue edge following");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();
        }
    }
}

/**
 * @brief Pair a positive integer
 *
 * Rule:
 * If the input positive integer n is odd, return n+1;
 * If n is even, return n-1.
 * Formula:
 * If n % 2 == 1, output n+1
 * If n % 2 == 0, output n-1
 *
 * @param n positive integer
 * @return int paired value
 */
int NavigationMowerAlg::PairNumber(int n)
{
    // Check if n is odd
    if (n % 2 == 1)
    {
        // If n is odd, return n+1
        return n + 1;
    }
    else
    {
        // If n is even, return n-1
        return n - 1;
    }
}

void NavigationMowerAlg::ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // Single area recharge, report based on recharge condition
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // Can calculate charging station QR code pose
        {
            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                //! 1. Get single area info
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetTimestampMs(), area, perimeter);
                }

                LOG_INFO("[BeaconDetection] area: {} , perimeter: {}", area, perimeter);

                {
                    master_region_explore_result_.is_exist = true;
                    master_region_explore_result_.area = area;
                    master_region_explore_result_.perimeter = perimeter;
                    master_region_explore_result_.charge_station_flag = true;
                    master_region_explore_result_.beacon_id = -1;

                    slave_region_explore_result_.is_exist = false;
                }

                //! 2. Report region exploration result
                RegionExploreResult region_explore_result;
                {
                    region_explore_result.result = true;
                    region_explore_result.timestamp = GetTimestampMs();
                    region_explore_result.master_region_map_result = master_region_explore_result_;
                    region_explore_result.slave_region_map_result = slave_region_explore_result_;
                }

                if (region_explore_result_callback_)
                {
                    region_explore_result_callback_(region_explore_result);
                }

                is_region_explore_mode_start_ = false;
            }
        }
    }
    else // Multi-region recharge, report directly
    {
        RegionExploreResult region_explore_result;
        {
            region_explore_result.result = true;
            region_explore_result.timestamp = GetTimestampMs();
            region_explore_result.master_region_map_result = master_region_explore_result_;
            region_explore_result.slave_region_map_result = slave_region_explore_result_;
        }

        if (region_explore_result_callback_)
        {
            region_explore_result_callback_(region_explore_result);
        }

        is_region_explore_mode_start_ = false;
    }
}

void NavigationMowerAlg::ResetAndActivateCooldown()
{
    // Reset cooldown timestamp and activate cooldown mechanism
    last_cooldown_time_ = std::chrono::steady_clock::now();
    is_cooldown_active_ = true;
}

void NavigationMowerAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // Cooldown mechanism active
    {
        LOG_DEBUG("[BeaconDetection] Cooldown mechanism activated");

        auto current_time = std::chrono::steady_clock::now();
        edge_perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print time difference
        LOG_DEBUG("[BeaconDetection] Edge perception drive cooldown timer (seconds): ({})", edge_perception_drive_duration_.count());
        HandleEdgeCooldownMechanism(mark_loc_result, is_cooldown_active, edge_perception_drive_duration_, edge_perception_drive_cooldown_time_threshold_);
    }
    else // Cooldown mechanism not active
    {
        LOG_DEBUG("[BeaconDetection] Cooldown mechanism not activated");

        LOG_DEBUG("[BeaconDetection] Enable perception drive, disable edge following");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result);
    }
}

void NavigationMowerAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, bool &is_cooldown_active,
                                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // Perception detection result
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[BeaconDetection] Timer exceeded ({}s), cooldown ended", perception_drive_cooldown_time_threshold);

        LOG_INFO("[BeaconDetection] Enable perception drive, disable edge following");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result);
    }
    else
    {
        // Cooldown not finished, skip execution
        LOG_INFO("[BeaconDetection] Cooldown not finished, not exceeding ({}s)", perception_drive_cooldown_time_threshold);

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        LOG_INFO("[BeaconDetection] Enable edge following");
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        mcu_triggers_region_exploration_)                  /* MCU exploration request */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] Exploration mode. Start unstake mode");
        PerformUnstakeMode(); // Execute unstake operation
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        mcu_triggers_region_exploration_)                  /* MCU exploration request */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] Exploration mode. Start unstake mode");

        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
    }
}

void NavigationMowerAlg::HandleExplorationMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // If charging station QR code detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::PER_FOUND_QR_CODE ||
         recharge_state == RechargeRunningState::PROCESS_CROSSREGION)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessExplorationRechargeException(recharge_state);
    }
    // If beacon QR code detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // Beacon found /**Cannot perform recovery mode */
    {
        ProcessExplorationCrossRegionException(cross_region_state);
    }
    // Other exceptions, enter recovery mode
    else
    {
        ProcessRecoveryException();
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessExplorationRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Charging station QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_region_exploration_)                    /* MCU exploration request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Beacon QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_region_exploration_)                    /* MCU exploration request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::ShowExplorePrint(BehaviorRunningState &behavior_state)
{
    LOG_INFO_THROTTLE(1500, "[BeaconDetection] Region exploration count: ({})", region_count_);
}

//==============================================================================
// Exception handling functions
//==============================================================================
void NavigationMowerAlg::HandleMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // If charging station QR code detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::PER_FOUND_QR_CODE ||
         recharge_state == RechargeRunningState::PROCESS_CROSSREGION)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessRechargeException(recharge_state);
    }
    // If beacon QR code detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON && // Perception detects beacon
             cross_region_state != CrossRegionRunningState::UNDEFINED)          // Beacon found state /**Cannot perform recovery mode */
    {
        ProcessCrossRegionException(cross_region_state);
    }
    else
    {
        ProcessRecoveryException();
        // if (is_slipping_detected_.load())
        // {
        //     ProcessRecoverySlipException();
        //     // SetSlippingStatus(true);
        // }
        // else
        // {
        //     ProcessRecoveryException();
        // }
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Charging station QR code detected");

    // MCU publishes recharge request
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_recharge_ &&                            /* MCU recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */

    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Beacon QR code detected");

    // MCU publishes cross-region request
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        mcu_triggers_cross_region_ &&                        /* MCU cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle other exceptions, enter recovery mode
void NavigationMowerAlg::ProcessRecoveryException()
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Handle collision or lifting exception");

    if ((!is_enable_unstake_mode_ || is_unstake_success_)) /* Unstake mode not enabled or already successful (first stage complete) */
    {
        thread_control_ = ThreadControl::BEHAVIOR_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle other exceptions, enter recovery mode (slip)
void NavigationMowerAlg::ProcessRecoverySlipException()
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Handle slip exception");

    // HandleRecoveryStart();

    thread_control_ = ThreadControl::BEHAVIOR_THREAD;
    UpdateFeatureSelection(thread_control_);
}

//==============================================================================
// Normal state handling functions
//==============================================================================

// Handle different stages in normal state
void NavigationMowerAlg::HandleNormalOperation(CrossRegionRunningState cross_region_state, RechargeRunningState recharge_state,
                                               BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    case ThreadControl::CROSS_REGION_THREAD:
    case ThreadControl::RECHARGE_THREAD:
    case ThreadControl::RANDOM_MOWING_THREAD:
    case ThreadControl::SPIRAL_MOWING_THREAD:
    {
        ProcessRandomMowing();
        ProcessSpiralMowing();
        ProcessCrossRegionMode(cross_region_state);
        ProcessRechargeMode(recharge_state);

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");

        break;
    }
}
void NavigationMowerAlg::CheckVelocityAndUpdateState(BehaviorRunningState &behavior_state)
{
    // Get current linear and angular velocity from motor speed
    MotorSpeedData motor_speed_data;
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
    }

    float v_left = motor_speed_data.motor_speed_left * 2 * M_PI / 60.0f * wheel_radius_;   // Left wheel speed (m/s)
    float v_right = motor_speed_data.motor_speed_right * 2 * M_PI / 60.0f * wheel_radius_; // Right wheel speed (m/s)
    float act_linear = (v_right + v_left) / 2.0f;                                          // Actual linear velocity (m/s)
    float act_angular = (v_right - v_left) / wheel_base_;                                  // Actual angular velocity (rad/s)

    // Check if velocity is close to zero
    if (std::abs(act_linear) < linear_velocity_threshold_ &&
        std::abs(act_angular) < angular_velocity_threshold_)
    {
        if (!is_velocity_zero_)
        {
            // Velocity is close to zero for the first time, start timing
            last_zero_velocity_time_ = std::chrono::steady_clock::now();
            is_velocity_zero_ = true;
            LOG_INFO("[BeaconDetection] Velocity is close to zero, start timing");
        }
        else
        {
            // Velocity remains close to zero, check duration
            auto current_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                current_time - last_zero_velocity_time_);

            if (duration.count() >= ZERO_VELOCITY_THRESHOLD_SECONDS)
            {
                // Velocity close to zero for 1 second, end recovery mode
                LOG_ERROR("[BeaconDetection] Velocity close to zero for 1 second, ending recovery mode");
                thread_control_ = ThreadControl::UNDEFINED;
                UpdateFeatureSelection(thread_control_);
                is_velocity_zero_ = false; // Reset flag

                // HandleRecoveryEnd(); // Notify suspended unstake tasks to continue
                // SetSlippingStatus(false);
            }
        }
    }
    else
    {
        // Velocity exceeds threshold, reset timer
        if (is_velocity_zero_)
        {
            LOG_INFO("[BeaconDetection] Velocity is no longer close to zero, reset timer");
            is_velocity_zero_ = false;
        }
    }

    if (behavior_state == BehaviorRunningState::SUCCESS)
    {
        LOG_ERROR("[BeaconDetection] Behavior state SUCCESS, ending recovery mode");
        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);
        is_velocity_zero_ = false; // Reset flag

        // HandleRecoveryEnd(); // Notify suspended unstake tasks to continue
        // SetSlippingStatus(false);
    }
}

// Stage 1: Unstake handling
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] Powered, start unstake task...");
        PerformUnstakeMode(); // Execute unstake operation
    }
}

// Stage 1: Unstake handling (QR code localization version)
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] Powered, start unstake task...");

        is_unstaking_ = true;

        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
        // PerformUnstakeModeAsync(qrcode_loc_result);
    }
}

// Stage 1: Pre-mowing processing
void NavigationMowerAlg::PreProcessingMowing(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        !is_power_connected_ &&                            /* Robot not powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[PreProcessingMowing] Not powered, start pre-mowing logic");

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE &&                                    // Can calculate charging station QR code pose
            sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) <= mower_start_qr_distance_threshold_) // 0.8
        {
            float mower_start_qr_distance = sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2));

            ControlLinearMotion(unstake_distance_ - mower_start_qr_distance, 0.0, unstake_vel_linear_, -1); // Exit charging station
            ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);                            // Rotate 45 degrees away from charging station
            PublishVelocity(0.0, 0.0, 1000);                                                                // Stop for 1s

            if (is_on_grass_field_) // On grass
            {
                is_unstake_mode_completed_ = true; // Unstake completed
                is_unstake_success_ = true;        // Unstake successful

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
                LOG_INFO("[BeaconDetection] On grass. Unstake mode succeeded, start mowing mode");
            }
            else // Not on grass
            {
                is_unstake_mode_completed_ = true; // Unstake completed
                is_unstake_success_ = false;       // Unstake failed

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
                LOG_ERROR("[BeaconDetection] Not on grass. Unstake mode failed, report error");

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
            }
        }
        else
        {
            is_unstake_mode_completed_ = true;
            is_unstake_success_ = true;
            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_);
        }
    }
}

// Stage 2: Random mowing handling
void NavigationMowerAlg::ProcessRandomMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        mcu_triggers_mower_ &&                               /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        // Check non-grass state during mowing
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS) // 5s
        {
            LOG_WARN("[BeaconDetection] Non-grass detected for more than {} seconds in random mowing mode, report WARN exception", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection] Non-grass detected for more than {} seconds in random mowing mode, report ERROR exception", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // Add in Run() or state machine loop
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // Stop publishing
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection] Start random mowing mode");
            PerformRandomMowing(); // Execute random mowing operation
        }
    }
}

// Stage 3: Spiral mowing handling
void NavigationMowerAlg::ProcessSpiralMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        mcu_triggers_spiral_mower_)                          /* MCU spiral mowing request */
    {
        // Check non-grass state during mowing
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS)
        {
            LOG_WARN("[BeaconDetection] Non-grass detected for more than {} seconds in spiral mowing mode, report WARN exception", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection] Non-grass detected for more than {} seconds in spiral mowing mode, report ERROR exception", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // Add in Run() or state machine loop
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // Stop publishing
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection] Start spiral mowing mode");
            PerformSpiralMowing(); // Execute spiral mowing operation
        }
    }
}

// Stage 4: Cross-region handling
void NavigationMowerAlg::ProcessCrossRegionMode(CrossRegionRunningState cross_region_state)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        mcu_triggers_cross_region_ &&                        /* MCU cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Start cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Stage 5: Recharge handling
void NavigationMowerAlg::ProcessRechargeMode(RechargeRunningState recharge_state)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        mcu_triggers_recharge_ &&                            /* MCU recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);

        // TODO: Consider whether to support cross-region recharge
    }
}

void NavigationMowerAlg::ResetMowerAlgFlags()
{
    // Runtime variables
    is_on_docker_ = false;
    mower_running_state_ = MowerRunningState::STOP;
    thread_control_ = ThreadControl::UNDEFINED; // Does not affect recharge function
    random_mower_state_ = RandomMowerRunningState::NORMAL;
    is_unstake_mode_completed_ = false;
    is_unstake_success_ = false;
    is_power_connected_ = false;

    mcu_triggers_cross_region_ = false;
    mcu_triggers_recharge_ = false;
    mcu_triggers_mower_ = false;
    mcu_triggers_spiral_mower_ = false;
    mcu_triggers_region_exploration_ = false;
    mcu_triggers_cut_border_ = false;

    is_region_explore_mode_start_ = false;
    is_cut_border_mode_start_ = false;

    mower_completed_ = false;
    frames_.clear();
    is_on_grass_field_ = false;

    UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    UpdateRechargeRunningState(RechargeRunningState::UNDEFINED);

    // New
    is_cooldown_active_ = false;
    is_first_enter_last_cooldown_time_ = true;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    edge_perception_drive_duration_ = std::chrono::seconds(0);

    enter_multi_region_exploration_ = false;

    // Single area
    qr_code_detection_count_ = 1;
    is_first_enter_explore_last_qr_detection_time_ = true;
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    qr_detection_duration_ = std::chrono::seconds(0);
    is_single_area_recharge_ = false;

    // Multi-area
    beacon_status_ = BeaconStatus(-1, 0);
    current_mark_id_ = -1;
    is_first_enter_last_mark_detection_time_ = true;
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    mark_detection_duration_ = std::chrono::seconds(0);

    first_detection_beacon_ = true;
    next_paired_beacon_id_ = -1; // Next paired beacon id

    region_count_ = 1; // Default 1 region

    // Region exploration info
    is_master_region_ = true;
    is_first_region_explore_mode_end_ = true;

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    /** Mowing non-grass exception detection */
    is_first_non_grass_detection_ = true;
    last_grass_time_ = std::chrono::steady_clock::now();
    non_grass_duration_ = std::chrono::seconds(0);

    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = false;

    /** Self-check mode */
    is_self_checking_ = false;                                 // Whether self-check is in progress
    self_check_start_time_ = std::chrono::steady_clock::now(); // Self-check start time
    is_self_success_ = false;
    is_self_recovery_active_ = false;

    /** cut border */
    is_first_enter_cut_border_last_qr_detection_time_ = true;
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    is_first_cut_border_mode_end_ = true;

    // Slip detection
    last_zero_velocity_time_ = std::chrono::steady_clock::now(); // Record start time when velocity is close to 0
    is_velocity_zero_ = false;                                   // Mark if current velocity is close to 0

    is_recovery_active_.store(false);

    // Wait for async task to finish (if started)
    if (unstake_future_.valid())
    {
        unstake_future_.wait(); // Or consider wait_for with timeout
    }

    // Unstake
    is_unstaking_ = false;

    last_imu_timestamp_ = 0;
    motion_detection_timestamp_ = 0;

    SetResetAllStuckStates();
}

void NavigationMowerAlg::SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result)
{
    if (save_qr_data_)
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            qr_detect_x_.push_back(qrcode_loc_result.xyzrpw.x);
            qr_detect_y_.push_back(qrcode_loc_result.xyzrpw.y);
            qr_detect_yaw_.push_back(qrcode_loc_result.xyzrpw.w);
        }
    }
}

/**
 * @brief
 *
 * @param qr_x_set qr_y_set qr_yaw_set
 * @return qr_x_avg qr_y_avg qr_yaw_avg
 */
std::vector<float> NavigationMowerAlg::Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set)
{
    std::sort(qr_x_set.begin(), qr_x_set.end());
    std::sort(qr_y_set.begin(), qr_y_set.end());
    std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    while ((qr_x_set[qr_x_set.size() - 1] - qr_x_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_x_set.size(); i++)
        {
            std::cout << qr_x_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_x_set.size() <= 2)
        {
            break;
        }
        qr_x_set.erase(qr_x_set.begin());
        qr_x_set.pop_back();
        std::sort(qr_x_set.begin(), qr_x_set.end());
    }
    while ((qr_y_set[qr_y_set.size() - 1] - qr_y_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_y_set.size(); i++)
        {
            std::cout << qr_y_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_y_set.size() <= 2)
        {
            break;
        }
        qr_y_set.erase(qr_y_set.begin());
        qr_y_set.pop_back();
        std::sort(qr_y_set.begin(), qr_y_set.end());
    }
    while ((qr_yaw_set[qr_yaw_set.size() - 1] - qr_yaw_set[0]) > 0.3)
    {
        for (int i = 0; i < qr_yaw_set.size(); i++)
        {
            std::cout << qr_yaw_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_yaw_set.size() <= 2)
        {
            break;
        }
        qr_yaw_set.erase(qr_yaw_set.begin());
        qr_yaw_set.pop_back();
        std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    }
    float qr_x_avg = std::accumulate(qr_x_set.begin(), qr_x_set.end(), 0.0) / qr_x_set.size();
    float qr_y_avg = std::accumulate(qr_y_set.begin(), qr_y_set.end(), 0.0) / qr_y_set.size();
    float qr_yaw_avg = std::accumulate(qr_yaw_set.begin(), qr_yaw_set.end(), 0.0) / qr_yaw_set.size();
    LOG_DEBUG("QRCodeData : qr_x_avg:{}, qr_y_avg:{}, qr_yaw_avg:{}.", qr_x_avg, qr_y_avg, qr_yaw_avg);
    std::vector<float> qr_data;
    qr_data.push_back(qr_x_avg);
    qr_data.push_back(qr_y_avg);
    qr_data.push_back(qr_yaw_avg);
    return qr_data;
}

/**
 * @brief Collect QR code data
 *
 * @return QR data
 */
std::vector<float> NavigationMowerAlg::Collect_QRdata()
{
    PublishVelocity(0, 0, stay_time_);
    save_qr_data_ = true;
    qr_detect_x_.clear();
    qr_detect_y_.clear();
    qr_detect_yaw_.clear();
    int record_times_ = 0;
    int try_times_ = 0;
    while (!(qr_detect_x_.size() >= save_data_num_))
    {
        PublishVelocity(0, 0, stay_time_);
        record_times_++;
        LOG_DEBUG("Stay And Collect Data, record_time :{}", record_times_ * stay_time_);
        if (record_times_ >= (stay_all_time_ / stay_time_))
        {
            LOG_DEBUG("Qrcode Collect Timeout!");
            std::vector<float> qr_error_data{0, 0, 0};
            return qr_error_data;
        }
    }
    save_qr_data_ = false;
    std::vector<float> qr_avg_data = Process_QRdata(qr_detect_x_, qr_detect_y_, qr_detect_yaw_);
    return qr_avg_data;
}

void NavigationMowerAlg::ProcessQRcodeAndUnstake(const std::vector<float> &qrcode_result)
{
    if (fabs(qrcode_result[0]) == 0)
    {
        LOG_DEBUG("QRCodeData Failed And Turn 45 Angle");
        ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
    }
    else
    {
        if (fabs(qrcode_result[1]) > (charge_station_width_ / 2))
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And More Than Charge Width And Turn Angle");
                ControlRotaryMotion(M_PI * angle_proportion_ + fabs(qrcode_result[2]), 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
            }
            else
            {
                LOG_DEBUG("Y < 0 And More Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                    0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
            }
        }
        else
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2((charge_station_width_ / 2) - fabs(qrcode_result[1]),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                    0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
            }
            else
            {
                LOG_DEBUG("Y < 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
            }
        }
    }
}

void NavigationMowerAlg::PerformUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    LOG_ERROR("[PerformUnstakeMode1] Start executing unstake operation");

    ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
    // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // Rotate 45 degrees away from charging station
    std::vector<float> stable_data = Collect_QRdata();
    LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
    ProcessQRcodeAndUnstake(stable_data);
    PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

    if (is_on_grass_field_) // On grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = true;        // Unstake successful

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
        LOG_INFO("[PerformUnstakeMode1] On grass. Unstake mode succeeded, start mowing mode");
    }
    else // Not on grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = false;       // Unstake failed

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
        LOG_ERROR("[PerformUnstakeMode1] Not on grass. Unstake mode failed, report error");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

void NavigationMowerAlg::PerformUnstakeModeAsync(const QRCodeLocationResult &qrcode_loc_result)
{
    // If an unstake task is already running, do not start a new one
    if (unstake_future_.valid() && unstake_future_.wait_for(std::chrono::seconds(0)) != std::future_status::ready)
    {
        LOG_WARN("[PerformUnstakeModeAsync1] Unstake task already running, skip new task");
        return;
    }

    // Start async task to execute unstake operation
    unstake_future_ = std::async(std::launch::async, [this, qrcode_loc_result]() {
        std::unique_lock<std::mutex> lock(recovery_mutex_);

        // while (is_recovery_active_)
        // {
        //     LOG_INFO("[PerformUnstakeModeAsync1] Recovery operation detected, suspend unstake task");
        //     recovery_cv_.wait(lock); // Wait for recovery operation to complete
        // }

        LOG_ERROR("[PerformUnstakeModeAsync1] Start executing unstake operation");
        ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
        std::vector<float> stable_data = Collect_QRdata();
        LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
        ProcessQRcodeAndUnstake(stable_data);
        PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

        if (is_on_grass_field_) // On grass
        {
            is_unstake_mode_completed_ = true; // Unstake completed
            is_unstake_success_ = true;        // Unstake successful

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
            LOG_ERROR("[PerformUnstakeModeAsync1] On grass. Unstake mode succeeded, start mowing mode");
        }
        else // Not on grass
        {
            is_unstake_mode_completed_ = true; // Unstake completed
            is_unstake_success_ = false;       // Unstake failed

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
            LOG_ERROR("[PerformUnstakeModeAsync1] Not on grass. Unstake mode failed, report error");

            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
        }
    });
}
void NavigationMowerAlg::HandleRecoveryStart()
{
    std::lock_guard<std::mutex> lock(recovery_mutex_);
    is_recovery_active_ = true;
    LOG_INFO("[HandleRecoveryStart1] Recovery operation started, suspending unstake task");
}

void NavigationMowerAlg::HandleRecoveryEnd()
{
    {
        std::lock_guard<std::mutex> lock(recovery_mutex_);
        is_recovery_active_ = false;
    }
    recovery_cv_.notify_all(); // Notify suspended unstake tasks to continue
    LOG_INFO("[HandleRecoveryEnd1] Recovery operation ended, resuming unstake task");
}

void NavigationMowerAlg::SetSlippingStatus(bool is_slipping)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetSlippingStatus(is_slipping); // Set slipping status
    }
}

void NavigationMowerAlg::PerformUnstakeMode()
{
    ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
    ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // Rotate 45 degrees away from charging station

    PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

    if (is_on_grass_field_) // On grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = true;        // Unstake successful

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
        LOG_INFO("[PerformUnstakeMode] On grass. Unstake mode succeeded, start mowing mode");
    }
    else // Not on grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = false;       // Unstake failed

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
        LOG_ERROR("[PerformUnstakeMode] Not on grass. Unstake mode failed, reporting error");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

/**
 * @brief Determine if on grass field
 *
 * @param segment_detect_result
 * @return true
 * @return false
 */
bool NavigationMowerAlg::IsGrassField(const GrassDetectStatus &grass_detect_status)
{
    // Add new status to queue
    frames_.push_back(grass_detect_status);

    // If queue is full (more than 10 frames), remove the oldest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass, no obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass and obstacles (part grass, part obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // Grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE)
            {
                grass_count++; // Grass
            }
        }
        LOG_DEBUG("[BeaconDetection] Grass count grass_count({})", grass_count);

        int grass_count_threshold = 7;
        if (int(grass_count) > grass_count_threshold) // Determine if it's grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

void NavigationMowerAlg::PerformRandomMowing()
{
    thread_control_ = ThreadControl::RANDOM_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::PerformSpiralMowing()
{
    thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection] Exit cross-region from non-grass to grass");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_);
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection] Counterclockwise edge following, turning right by a certain angle");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1;
        }
        else
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now();

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetTimestampMs());
                // PublishVelocity(0.0, 0.0, 5000);
            }
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection] Exit cross-region by beacon detection");

        if (edge_mode_direction_ == 1)
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_);
        }
        else
        {
            LOG_INFO("[BeaconDetection] Counterclockwise edge following, turning right by a certain angle!");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1;
        }
        else
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now();

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetTimestampMs());
                // PublishVelocity(0.0, 0.0, 5000);
            }
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
}

void NavigationMowerAlg::HandleNormalCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection] Exit cross-region from non-grass to grass");

        ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
        ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection] Exit cross-region by beacon detection");

        ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
        ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
}

bool NavigationMowerAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationMowerAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

void NavigationMowerAlg::UpdateRechargeRunningState(RechargeRunningState state)
{
    if (recharge_running_state_callback_)
    {
        recharge_running_state_callback_(state);
    }
}

void NavigationMowerAlg::SetUndockResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status)
{
    if (undock_result_callback_)
    {
        undock_result_callback_(completed, result, status);
    }
}

void NavigationMowerAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    for (int i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            shortest_dis_inx = i;
        }
    }
}

void NavigationMowerAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // Left deviation, turn left
        LOG_INFO("Perception-based adjustment: turning left!");
        PublishVelocity(0, mower_angular_);
        break;

    case 0: // Centered, go straight
        LOG_INFO("Perception-based adjustment: moving straight!");
        PublishVelocity(mower_linear_, 0);
        break;

    case 1: // Right deviation, turn right
        LOG_INFO("Perception-based adjustment: turning right!");
        PublishVelocity(0, -mower_angular_);
        break;

    default:
        LOG_INFO("Perception-based adjustment: invalid mark_perception_direction flag!");
        PublishVelocity(mower_linear_, 0);
        break;
    }
}

void NavigationMowerAlg::UpdateFeatureSelection(const ThreadControl &thread_control)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData rechgarge{ThreadControl::RECHARGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData edge_follow{ThreadControl::PERCEPTION_EDGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData cross_region{ThreadControl::CROSS_REGION_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData random_mower{ThreadControl::RANDOM_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData behavior{ThreadControl::BEHAVIOR_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData spiral_mower{ThreadControl::SPIRAL_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData escape{ThreadControl::ESCAPE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};

    switch (thread_control)
    {
    case ThreadControl::PERCEPTION_EDGE_THREAD:                               // Edge following: includes escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Edge following: includes escape");
        break;

    case ThreadControl::CROSS_REGION_THREAD:                                  // Cross region: includes edge following, escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);   // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape");
        break;

    case ThreadControl::RECHARGE_THREAD:                                      // Recharge: includes edge following, cross region, escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);     // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Recharge: includes edge following, cross region, escape");
        break;

    case ThreadControl::RANDOM_MOWING_THREAD:
    {
        if (random_mower_state_ == RandomMowerRunningState::NORMAL)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        else if (random_mower_state_ == RandomMowerRunningState::TRAP_WAIT_BIAS)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. Escape
        }
        else if (random_mower_state_ == RandomMowerRunningState::TRAP_EDGE_FOLLOW)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE); // 1. Random mowing
            // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        else
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Random mowing:");
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:                                      // Recovery:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);      // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Recovery:");
        break;

    case ThreadControl::SPIRAL_MOWING_THREAD:                                 // Spiral mowing:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Spiral mowing:");
        break;

    case ThreadControl::CLOSE_ALL_TASK:                                       // Close all tasks:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Close all tasks:");
        break;

    case ThreadControl::UNDEFINED:                                            // Undefined:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Undefined:");
        break;

    default:
        // random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        // behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        // spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        break;
    }

    feature_data.push_back(random_mower);
    feature_data.push_back(edge_follow);
    feature_data.push_back(cross_region);
    feature_data.push_back(rechgarge);
    feature_data.push_back(behavior);
    feature_data.push_back(spiral_mower);
    feature_data.push_back(escape);

    if (feature_select_callback_)
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::SetMowerAlgParam(const MowerAlgParam &param)
{
    is_enable_unstake_mode_ = param.is_enable_unstake_mode;
    unstake_distance_ = param.unstake_distance;       // Unstake distance /*param*/
    unstake_adjust_yaw_ = param.unstake_adjust_yaw;   // Unstake adjustment angle /*param*/
    unstake_vel_linear_ = param.unstake_vel_linear;   // Unstake linear velocity /*param*/
    unstake_vel_angular_ = param.unstake_vel_angular; // Unstake angular velocity /*param*/

    // Algorithm parameters
    mower_linear_ = param.mower_linear;   /*param*/
    mower_angular_ = param.mower_angular; /*param*/
    // perception_drive_cooldown_time_ = param.perception_drive_cooldown_time; // Perception drive cooldown time 5s /*param*/
    edge_mode_direction_ = param.edge_mode_direction;                   // Default counterclockwise -1 /*param*/
    cross_region_adjust_yaw_ = param.cross_region_adjust_yaw;           // Yaw adjustment after cross-region /*param*/
    cross_region_adjust_displace_ = param.cross_region_adjust_displace; // Displacement adjustment after cross-region /*param*/
    mark_distance_threshold_ = param.mark_distance_threshold;           // 1.5 Beacon distance threshold relative to robot camera, used to determine if within region /*param*/
    camera_2_center_dis_ = param.camera_2_center_dis;                   // Distance from robot camera to rotation center is 0.45 /*param*/

    edge_perception_drive_cooldown_time_threshold_ = param.edge_perception_drive_cooldown_time_threshold; // 10s Edge perception drive cooldown time  /*param*/
    qr_detection_cooldown_time_threshold_ = param.qr_detection_cooldown_time_threshold;                   // 60s QR code detection cooldown time  /*param*/
    mark_detection_cooldown_time_threshold_ = param.mark_detection_cooldown_time_threshold;               // 60s Beacon detection cooldown time  /*param*/

    // Stuck detection data logging control
    enable_stuck_detection_data_logging_ = param.enable_stuck_detection_data_logging; // Enable stuck detection data logging /*param*/
}

void NavigationMowerAlg::SetMowerRunningState(MowerRunningState state)
{
    LOG_INFO_THROTTLE(1000, "NavigationMowerAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_WARN("[NavigationMowerAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationMowerAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationMowerAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationMowerAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationMowerAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationMowerAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_DEBUG_THROTTLE(1000, "[BeaconDetection] Before coordinate conversion MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
                             "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
                       mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                       mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                       mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                       Radians2Degrees(mark_loc_result.xyzrpw.w));

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 means in ROI area
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 means not in ROI area
    }
    else
    {
        mark_loc_result.roi_confidence = -1; // -1 means detection failed
    }

    // 2. Convert camera-to-mark (right-hand) coordinates to base_link-to-mark coordinates
    if (mark_loc_result.detect_status == 2)
    {
        // Camera to base_link fixed coordinates
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // Camera to mark coordinates
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // Calculate base_link relative to mark
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_DEBUG("[BeaconDetection] After coordinate conversion MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                  "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                  mark_loc_result.detect_status,
                  mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                  mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                  mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                  mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                  Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationMowerAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationMowerAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationMowerAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationMowerAlg::SetRechargeRunningStateCallback(std::function<void(RechargeRunningState)> callback)
{
    recharge_running_state_callback_ = callback;
}

void NavigationMowerAlg::SetUndockResultCallback(std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> callback)
{
    undock_result_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStartCallback(std::function<bool(uint64_t)> callback)
{
    area_calc_start_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStopCallback(std::function<bool(uint64_t, float &, float &)> callback)
{
    area_calc_stop_callback_ = callback;
}

void NavigationMowerAlg::SetRegionExploreResultCallback(std::function<void(RegionExploreResult &)> callback)
{
    region_explore_result_callback_ = callback;
}

void NavigationMowerAlg::SetCutBorderResultCallback(std::function<void(bool, bool)> callback)
{
    cut_border_result_callback_ = callback;
}

void NavigationMowerAlg::SetPerceptionLocalizationAlgCtrlCallback(std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> callback)
{
    perception_localization_alg_ctrl_callback_ = callback;
}

void NavigationMowerAlg::SetAppTriggersRecharge(bool is_recharge)
{
    mcu_triggers_recharge_ = is_recharge;
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU recharge request: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
}

void NavigationMowerAlg::SetAppTriggersCrossRegion(bool is_cross_region)
{
    mcu_triggers_cross_region_ = is_cross_region; // MCU triggers cross-region thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
}

void NavigationMowerAlg::SetAppTriggersMower(bool is_mower)
{
    mcu_triggers_mower_ = is_mower; // MCU triggers random mowing thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU mowing request: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
}

void NavigationMowerAlg::SetAppTriggersSpiralMower(bool is_mower)
{
    mcu_triggers_spiral_mower_ = is_mower; // MCU triggers spiral mowing thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
}

void NavigationMowerAlg::SetAppTriggersRegionExplore(bool is_region_explore)
{
    mcu_triggers_region_exploration_ = is_region_explore; // MCU triggers region exploration thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU region exploration request: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
}

void NavigationMowerAlg::SetAppTriggersCutBorder(bool is_cut_border)
{
    mcu_triggers_cut_border_ = is_cut_border; // MCU triggers cut border thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection] MCU cut border request: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));
}

void NavigationMowerAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU recharge request: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU cross-region request: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU (random) mowing request: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU spiral mowing request: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU exploration request: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection] MCU cut border request: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));

#if (TEST == 0)
    /********************************************************Undocking print****************************************************************** */
    if (is_enable_unstake_mode_ && !is_unstake_success_ &&                                                                                                /* Unstake mode enabled && not successful */
        !is_power_connected_ &&                                                                                                                           /* Robot not powered */
        (mcu_triggers_cut_border_ || mcu_triggers_region_exploration_ || mcu_triggers_mower_)) /* MCU cut border request */ /* MCU exploration request */ /* MCU mowing request */
    {
        if (is_unstake_mode_completed_) // Unstake completed
        {
            LOG_ERROR_THROTTLE(1000, "[BeaconDetection] Unstake failed, reporting error state!");
        }
        else // Unstake not completed
        {
            // LOG_WARN_THROTTLE(1000, "[BeaconDetection] Mower not powered, cannot unstake");
        }
    }

    /********************************************************Undocking print****************************************************************** */
#endif

    /********************************************************thread_control_****************************************************************** */
    // switch (thread_control_)
    // {
    // case ThreadControl::PERCEPTION_EDGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection] Running mode: Edge following({})", int(thread_control_));
    //     break;

    // case ThreadControl::CROSS_REGION_THREAD:
    //     LOG_DEBUG("[BeaconDetection] Running mode: Cross-region({})", int(thread_control_));
    //     break;

    // case ThreadControl::RECHARGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection] Running mode: Recharge({})", int(thread_control_));
    //     break;

    // case ThreadControl::RANDOM_MOWING_THREAD:
    //     LOG_DEBUG("[BeaconDetection] Running mode: Random mowing({})", int(thread_control_));
    //     break;

    // default:
    //     LOG_DEBUG("[BeaconDetection] Not running any mode");
    //     break;
    // }

    /********************************************************mark_loc_result****************************************************************** */

    // LOG_DEBUG("[BeaconDetection]  MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
    //           "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
    //           mark_loc_result.detect_status,
    //           mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
    //           mark_loc_result.roi_confidence, mark_loc_result.target_direction,
    //           mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
    //           mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
    //           Radians2Degrees(mark_loc_result.xyzrpw.w));

    // for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    // {
    //     LOG_DEBUG("[BeaconDetection] mark_id_distance beacon and distance: mark_id({}), distance({})",
    //               mark_id_distance.mark_id, mark_id_distance.distance);
    // }
}

void NavigationMowerAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationMowerAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationMowerAlg::CrossRegionDisable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, false);
}

void NavigationMowerAlg::CrossRegionEnable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, true);
}

void NavigationMowerAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // Default rotation direction 1.0 left, -1.0 right
    if (sign > 0)
    {
        LOG_INFO("[ControlRotaryMotion] Rotation direction: left");
    }
    else
    {
        LOG_INFO("[ControlRotaryMotion] Rotation direction: right");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // Rotation duration ms
    LOG_INFO("[ControlRotaryMotion] Rotation angle = {}", Radians2Degrees(ang_err));
    LOG_INFO("[ControlRotaryMotion] Angular velocity = {}, time = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

void NavigationMowerAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                             const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[ControlLinearMotion] Linear distance dis = {}", dis);
    LOG_INFO("[ControlLinearMotion] Linear speed = {}, time = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

void NavigationMowerAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_iceoryx_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void NavigationMowerAlg::PublishSlipException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_iceoryx_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
    }
}

void NavigationMowerAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation Mower publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}
//==============================================================================
// Region exploration function handlers
//==============================================================================

void NavigationMowerAlg::ProcessCutBorderUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot powered */
        mcu_triggers_cut_border_)                          /* MCU cut border request */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] Cut border mode. Start unstake mode");
        PerformUnstakeMode(); // Execute unstake operation
    }
}
void NavigationMowerAlg::ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot powered */
        mcu_triggers_cut_border_)                          /* MCU cut border request */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] Cut border mode. Start unstake mode");
        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
    }
}

void NavigationMowerAlg::HandleCutBorderMcuException(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state)
{
    // If charging station QR code is detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::PER_FOUND_QR_CODE ||
         recharge_state == RechargeRunningState::PROCESS_CROSSREGION)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessCutBorderRechargeException(recharge_state);
    }
    // If beacon QR code is detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // Beacon found /**Cannot perform recovery mode */
    {
        ProcessCutBorderCrossRegionException(cross_region_state);
    }
    // Other exceptions, enter recovery mode
    else
    {
        ProcessRecoveryException();
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessCutBorderRechargeException(RechargeRunningState recharge_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Charging station QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_cut_border_)                            /* MCU cut border request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection] Beacon QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_cut_border_)                            /* MCU cut border request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::PerformCutBorder(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                          CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In edge-following or undefined mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In edge-following or undefined mode");

        bool is_beacon_valid = false; // Default: beacon invalid
        ProcessBeaconDetection(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaCutBorderMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaCutBorderMode(mark_loc_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In cross-region mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In cross-region mode");

        // Handle different cross-region states
        HandleCutBorderCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In recharge mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In recharge mode");

        ProcessingCutBorderRecharge(qrcode_loc_result);

        if (is_first_cut_border_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_cut_border_mode_end_ = false;
        }

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] In other function mode");

        break;
    }
}

/**
 * @brief Use charging station detection to determine when to stop edge following and switch to recharge
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                                        const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !enter_multi_region_exploration)                     /* Not entering multi-region exploration */
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start single area cut border mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection] Start single area cut border mode");

        if (qrcode_loc_result.detect_status != QRCodeDetectStatus::NO_DETECT_QRCODE)
        {
            auto current_time = std::chrono::steady_clock::now();
            qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_cut_border_detection_time_);

            LOG_INFO("[BeaconDetection] Charging station QR code detection cooldown timer (seconds): ({})", qr_detection_duration_.count());
            if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // Increase timer
            {
                qr_code_detection_count_++;
                last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
                LOG_INFO("[BeaconDetection] Detected valid charging station QR code pose, current detection count: {}", qr_code_detection_count_);
            }
        }

        // Use QR code detection to determine when to stop edge following and switch to recharge
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection] Detected valid recharge QR code pose twice, switching to recharge mode");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // Reset state
            qr_code_detection_count_ = 1;
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                                       const bool &enter_multi_region_exploration,
                                                       bool &is_beacon_valid)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        enter_multi_region_exploration)                      /* Entering multi-region exploration */
    {
        LOG_INFO("[BeaconDetection] Start multi-region exploration mode");

        if (is_beacon_valid) // Beacon is valid
        {
            // Reset cooldown timestamp and activate cooldown mechanism
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection] Current detected mark_id: {}", current_mark_id_);

            // Initialize only on first entry
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
                is_first_enter_last_mark_detection_time_ = false;
                LOG_INFO("[BeaconDetection] Beacon detection start timing");
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection] Current timestamp (seconds) current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection] Last timestamp (seconds) last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_INFO("[BeaconDetection] Beacon detection cooldown timer (seconds): ({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // Same mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] Same mark_id, current detected mark_id: {}", current_mark_id_);
                }
                else // Different mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection] Different mark_id, current detected mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection] Different mark_id, last detected mark_id: {}", beacon_status_.mark_id);
                }
            }
        }

        if (beacon_status_.beacon_look_count >= 2)
        {
            // Start cross-region process
            LOG_INFO("[BeaconDetection] Beacon detected more than twice, starting cross-region process");

            thread_control_ = ThreadControl::CROSS_REGION_THREAD;
            UpdateFeatureSelection(thread_control_);
            EdgeFollowDisable();
            is_single_area_recharge_ = false;

            // Reset state
            next_paired_beacon_id_ = PairNumber(current_mark_id_);
            beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
            LOG_INFO("[BeaconDetection] Next paired beacon id is {}", next_paired_beacon_id_);
        }
        else
        {
            // Continue edge following
            LOG_INFO("[BeaconDetection] Beacon detection not more than twice, continue edge following");

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();
        }
    }
}

void NavigationMowerAlg::HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION) // Stage 4, exit cross-region from non-grass to grass
    {
        LOG_INFO("[BeaconDetection] Exit cross-region from non-grass to grass");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // Turn left
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection] Counterclockwise edge following, turning right by a certain angle");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // Recharge mode
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // Restore default value
        }
        else
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // Edge following mode
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
            // is_first_enter_last_mark_detection_time_ = true; // Reset timer. If you can find the beacon after cross-region, you need to reset the timer
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION) //  Stage 4, exit cross-region by beacon detection
    {
        LOG_INFO("[BeaconDetection] Exit cross-region by beacon detection");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // Turn left
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection] Counterclockwise edge following, turning right by a certain angle!");
            ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // Recharge mode
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // Restore default value
        }
        else
        {
            LOG_INFO("[BeaconDetection] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // Edge following mode
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
            // is_first_enter_last_mark_detection_time_ = true; // Reset timer. If you can find the beacon after cross-region, you need to reset the timer
        }

        UpdateCrossRegionRunningState(CrossRegionRunningState::UNDEFINED);
    }
}

void NavigationMowerAlg::ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // Single area recharge, report based on recharge condition
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // Can calculate charging station QR code pose
        {
            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                if (cut_border_result_callback_)
                {
                    cut_border_result_callback_(true, true);
                }

                is_cut_border_mode_start_ = false;
            }
        }
    }
    else // Multi-region recharge, report directly
    {
        if (cut_border_result_callback_)
        {
            cut_border_result_callback_(true, true);
        }

        is_cut_border_mode_start_ = false;
    }
}

//==============================================================================
// Region exploration function handlers
//==============================================================================

void NavigationMowerAlg::TriggerExceptionPublishing()
{
    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = true;
}

void NavigationMowerAlg::InitStuckDetectionRecovery()
{
    LOG_INFO("[MowerAlg] Initialize stuck detection and recovery system");

    // Set stuck recovery parameters
    StuckRecoveryParam param;
    param.wheel_radius = wheel_radius_;
    param.wheel_base = wheel_base_;
    param.enable_data_logging = enable_stuck_detection_data_logging_; // Use parameter to control data logging switch
    param.log_file_path = "/userdata/log/stuck_recovery_data.csv";

    // Create stuck detection recovery instance
    stuck_detection_recovery_ = std::make_unique<StuckDetectionRecovery>(param);

    // Set velocity publisher - convert unique_ptr to shared_ptr
    stuck_detection_recovery_->SetVelocityPublisher(std::shared_ptr<VelocityPublisher>(vel_publisher_.get(), [](VelocityPublisher *) {}));

    // Set exception publisher callback
    stuck_detection_recovery_->SetExceptionPublisher([this](fescue_iox::SocExceptionLevel level, fescue_iox::SocExceptionValue value) {
        this->PublishException(static_cast<mower_msgs::msg::SocExceptionLevel>(level), static_cast<mower_msgs::msg::SocExceptionValue>(value));
    });

    // Initialize
    stuck_detection_recovery_->Initialize();

    LOG_INFO("[MowerAlg] Stuck detection data logging: {}", enable_stuck_detection_data_logging_ ? "Enabled" : "Disabled");
}

void NavigationMowerAlg::DeinitStuckDetectionRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Shutdown stuck detection and recovery system");
        stuck_detection_recovery_->Shutdown();
        stuck_detection_recovery_.reset();
    }
}

bool NavigationMowerAlg::IsStuckDetected()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsStuck();
    }
    return false;
}

bool NavigationMowerAlg::StartStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Start stuck recovery");
        return stuck_detection_recovery_->StartRecovery();
    }
    return false;
}

void NavigationMowerAlg::StopStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Stop stuck recovery");
        stuck_detection_recovery_->StopRecovery();
    }
}

bool NavigationMowerAlg::IsStuckRecoveryActive()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsRecoveryActive();
    }
    return false;
}

void NavigationMowerAlg::SetStuckDetectionActive(bool active)
{
    if (stuck_detection_recovery_)
    {
        if (active)
        {
            stuck_detection_recovery_->StartDetection();
        }
        else
        {
            stuck_detection_recovery_->StopDetection();
        }
    }
}

void NavigationMowerAlg::SetResetAllStuckStates()
{
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->ResetAllStates();
    }
}

bool NavigationMowerAlg::ShouldPerformStuckDetection()
{
    if (!stuck_detection_recovery_)
    {
        return false;
    }

    // If in recovery success cooldown period, should not perform stuck detection
    if (stuck_detection_recovery_->IsInRecoverySuccessCooldown())
    {
        return false;
    }

    return true;
}

} // namespace fescue_iox